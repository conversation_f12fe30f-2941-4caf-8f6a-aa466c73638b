/**
 * End-to-End Integration Testing Suite
 * Comprehensive integration tests for multilingual workflows and API compatibility
 */

import { TestUtils, TestResult, TEST_PHONE_NUMBERS } from './setup';
import { multilingualApiMiddleware } from '../services/multilingualApiMiddleware';
import { multilingualApiEnhancements } from '../services/multilingualApiEnhancements';
import { businessApi, productsApi, faqsApi } from '../services/api';
import { userLanguageService } from '../services/userLanguageService';
import { messageTranslationService } from '../services/messageTranslationService';
import type { SupportedLanguage } from '../types/translation';

/**
 * End-to-End Integration Test Suite
 */
export class EndToEndIntegrationTests {

  /**
   * Test complete WhatsApp message translation workflow
   */
  static async testWhatsAppMessageTranslationWorkflow(): Promise<TestResult> {
    const testName = 'WhatsApp Message Translation Workflow';
    console.log(`💬 Testing: ${testName}`);

    try {
      const phoneNumber = TEST_PHONE_NUMBERS.kenya;
      const businessId = 'test_business_123';
      
      const { result, duration } = await TestUtils.measureExecutionTime(async () => {
        const workflow: any[] = [];
        
        // Step 1: Incoming message in Swahili
        const incomingMessage = 'Habari, nataka kujua bei ya chakula';
        workflow.push({ step: 'incoming_message', message: incomingMessage });
        
        // Step 2: Detect language
        const detectedLanguage = await messageTranslationService.detectLanguage(incomingMessage);
        workflow.push({ step: 'language_detection', detected: detectedLanguage });
        
        // Step 3: Translate to English for processing
        const translatedToEnglish = await messageTranslationService.translateMessage(
          incomingMessage,
          detectedLanguage.language,
          'en',
          { businessId, phoneNumber }
        );
        workflow.push({ step: 'translate_to_english', translation: translatedToEnglish });
        
        // Step 4: Process business logic (simulated)
        const businessResponse = 'Our menu prices range from KSh 200 to KSh 1500. Would you like to see our full menu?';
        workflow.push({ step: 'business_response', response: businessResponse });
        
        // Step 5: Translate response back to user's language
        const translatedResponse = await messageTranslationService.translateMessage(
          businessResponse,
          'en',
          detectedLanguage.language,
          { businessId, phoneNumber }
        );
        workflow.push({ step: 'translate_response', translation: translatedResponse });
        
        // Step 6: Update user language preference
        await userLanguageService.recordLanguageDetection(
          phoneNumber,
          detectedLanguage.language,
          detectedLanguage.confidence
        );
        workflow.push({ step: 'update_preference', completed: true });
        
        return workflow;
      });

      // Validate workflow completion
      const completedSteps = result.filter(step => !step.error).length;
      const expectedSteps = 6;
      
      return {
        name: testName,
        passed: completedSteps === expectedSteps,
        duration,
        metadata: {
          completedSteps,
          expectedSteps,
          workflow: result
        }
      };

    } catch (error) {
      return {
        name: testName,
        passed: false,
        duration: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test language detection accuracy across phone number regions
   */
  static async testLanguageDetectionAccuracy(): Promise<TestResult> {
    const testName = 'Language Detection Accuracy Across Phone Regions';
    console.log(`📱 Testing: ${testName}`);

    try {
      const { result, duration } = await TestUtils.measureExecutionTime(async () => {
        const results: any[] = [];
        
        const phoneRegionTests = [
          { phone: TEST_PHONE_NUMBERS.kenya, expected: 'sw', country: 'Kenya' },
          { phone: TEST_PHONE_NUMBERS.tanzania, expected: 'sw', country: 'Tanzania' },
          { phone: TEST_PHONE_NUMBERS.nigeria, expected: 'ha', country: 'Nigeria' },
          { phone: TEST_PHONE_NUMBERS.ethiopia, expected: 'am', country: 'Ethiopia' },
          { phone: TEST_PHONE_NUMBERS.morocco, expected: 'ar', country: 'Morocco' },
          { phone: TEST_PHONE_NUMBERS.senegal, expected: 'fr', country: 'Senegal' },
          { phone: TEST_PHONE_NUMBERS.angola, expected: 'pt', country: 'Angola' },
          { phone: TEST_PHONE_NUMBERS.ghana, expected: 'en', country: 'Ghana' }
        ];
        
        for (const test of phoneRegionTests) {
          try {
            const context = TestUtils.createTestContext(test.phone);
            const detectedPreference = await multilingualApiMiddleware.detectLanguagePreference(context);
            
            const isCorrect = detectedPreference.language === test.expected;
            
            results.push({
              country: test.country,
              phone: test.phone,
              expected: test.expected,
              detected: detectedPreference.language,
              confidence: detectedPreference.confidence,
              source: detectedPreference.source,
              correct: isCorrect
            });
            
          } catch (error) {
            results.push({
              country: test.country,
              phone: test.phone,
              expected: test.expected,
              error: error instanceof Error ? error.message : String(error),
              correct: false
            });
          }
        }
        
        return results;
      });

      const correctDetections = result.filter(r => r.correct).length;
      const totalDetections = result.length;
      const accuracy = (correctDetections / totalDetections) * 100;
      
      return {
        name: testName,
        passed: accuracy >= 90, // 90% accuracy threshold
        duration,
        metadata: {
          correctDetections,
          totalDetections,
          accuracy: Math.round(accuracy),
          results: result
        }
      };

    } catch (error) {
      return {
        name: testName,
        passed: false,
        duration: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test API backward compatibility with existing consumers
   */
  static async testAPIBackwardCompatibility(): Promise<TestResult> {
    const testName = 'API Backward Compatibility';
    console.log(`🔄 Testing: ${testName}`);

    try {
      const mockBusiness = TestUtils.createMockBusiness();
      
      const { result, duration } = await TestUtils.measureExecutionTime(async () => {
        const results: any[] = [];
        
        // Test original API methods still work
        try {
          const business = await businessApi.getById(mockBusiness.id);
          results.push({
            api: 'businessApi.getById',
            success: !!business,
            hasExpectedStructure: business && 'name' in business && 'description' in business
          });
        } catch (error) {
          results.push({
            api: 'businessApi.getById',
            success: false,
            error: error instanceof Error ? error.message : String(error)
          });
        }
        
        // Test products API
        try {
          const products = await productsApi.getByBusinessId(mockBusiness.id);
          results.push({
            api: 'productsApi.getByBusinessId',
            success: Array.isArray(products),
            hasExpectedStructure: true
          });
        } catch (error) {
          results.push({
            api: 'productsApi.getByBusinessId',
            success: false,
            error: error instanceof Error ? error.message : String(error)
          });
        }
        
        // Test FAQs API
        try {
          const faqs = await faqsApi.getByBusinessId(mockBusiness.id);
          results.push({
            api: 'faqsApi.getByBusinessId',
            success: Array.isArray(faqs),
            hasExpectedStructure: true
          });
        } catch (error) {
          results.push({
            api: 'faqsApi.getByBusinessId',
            success: false,
            error: error instanceof Error ? error.message : String(error)
          });
        }
        
        return results;
      });

      const successfulAPIs = result.filter(r => r.success && r.hasExpectedStructure).length;
      const totalAPIs = result.length;
      
      return {
        name: testName,
        passed: successfulAPIs === totalAPIs,
        duration,
        metadata: {
          successfulAPIs,
          totalAPIs,
          compatibilityRate: (successfulAPIs / totalAPIs) * 100,
          results: result
        }
      };

    } catch (error) {
      return {
        name: testName,
        passed: false,
        duration: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test multilingual content bundle retrieval and response formatting
   */
  static async testMultilingualContentBundleRetrieval(): Promise<TestResult> {
    const testName = 'Multilingual Content Bundle Retrieval';
    console.log(`📦 Testing: ${testName}`);

    try {
      const mockBusiness = TestUtils.createMockBusiness();
      const context = TestUtils.createTestContext(TEST_PHONE_NUMBERS.kenya, 'sw');
      
      const { result, duration } = await TestUtils.measureExecutionTime(async () => {
        const results: any[] = [];
        
        // Test content bundle retrieval for different languages
        const languages: SupportedLanguage[] = ['sw', 'ha', 'fr'];
        
        for (const language of languages) {
          try {
            const bundle = await multilingualApiEnhancements.getBusinessContentBundle(
              mockBusiness.id,
              { lang: language, phoneNumber: context.phoneNumber }
            );
            
            results.push({
              language,
              success: true,
              hasBusiness: !!bundle.data.business,
              hasProducts: Array.isArray(bundle.data.products),
              hasFaqs: Array.isArray(bundle.data.faqs),
              hasLanguagePreference: !!bundle.data.languagePreference,
              requestedLanguage: bundle.requestedLanguage,
              hasTranslationMetadata: !!bundle.translation
            });
            
          } catch (error) {
            results.push({
              language,
              success: false,
              error: error instanceof Error ? error.message : String(error)
            });
          }
        }
        
        return results;
      });

      const successfulBundles = result.filter(r => 
        r.success && r.hasBusiness && r.hasProducts && r.hasFaqs && r.hasLanguagePreference
      ).length;
      const totalBundles = result.length;
      
      return {
        name: testName,
        passed: successfulBundles === totalBundles,
        duration,
        metadata: {
          successfulBundles,
          totalBundles,
          bundleSuccessRate: (successfulBundles / totalBundles) * 100,
          results: result
        }
      };

    } catch (error) {
      return {
        name: testName,
        passed: false,
        duration: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test cost tracking and analytics integration accuracy
   */
  static async testCostTrackingAndAnalyticsIntegration(): Promise<TestResult> {
    const testName = 'Cost Tracking and Analytics Integration';
    console.log(`💰 Testing: ${testName}`);

    try {
      const { result, duration } = await TestUtils.measureExecutionTime(async () => {
        const results: any[] = [];
        
        // Test analytics integration
        try {
          const businessId = 'test_business_123';
          const timeRange = {
            start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
            end: new Date()
          };
          
          const languageAnalytics = await multilingualApiEnhancements.getBusinessLanguageAnalytics(
            businessId,
            timeRange
          );
          
          results.push({
            test: 'language_analytics',
            success: true,
            hasLanguageUsage: Array.isArray(languageAnalytics.languageUsage),
            hasTranslationMetrics: !!languageAnalytics.translationMetrics,
            hasUserEngagement: !!languageAnalytics.userEngagement,
            hasCostAnalysis: !!languageAnalytics.costAnalysis
          });
          
        } catch (error) {
          results.push({
            test: 'language_analytics',
            success: false,
            error: error instanceof Error ? error.message : String(error)
          });
        }
        
        // Test user language preference tracking
        try {
          const phoneNumber = TEST_PHONE_NUMBERS.kenya;
          const language: SupportedLanguage = 'sw';
          
          const preferenceResponse = await multilingualApiEnhancements.setUserLanguagePreference(
            phoneNumber,
            language,
            true
          );
          
          results.push({
            test: 'preference_tracking',
            success: true,
            hasPhoneNumber: !!preferenceResponse.phoneNumber,
            hasPreferredLanguage: !!preferenceResponse.preferredLanguage,
            hasConfidence: typeof preferenceResponse.confidence === 'number',
            hasAvailableLanguages: Array.isArray(preferenceResponse.availableLanguages)
          });
          
        } catch (error) {
          results.push({
            test: 'preference_tracking',
            success: false,
            error: error instanceof Error ? error.message : String(error)
          });
        }
        
        return results;
      });

      const successfulTests = result.filter(r => r.success).length;
      const totalTests = result.length;
      
      return {
        name: testName,
        passed: successfulTests === totalTests,
        duration,
        metadata: {
          successfulTests,
          totalTests,
          integrationSuccessRate: (successfulTests / totalTests) * 100,
          results: result
        }
      };

    } catch (error) {
      return {
        name: testName,
        passed: false,
        duration: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test complete user journey from language detection to content delivery
   */
  static async testCompleteUserJourney(): Promise<TestResult> {
    const testName = 'Complete User Journey - Language Detection to Content Delivery';
    console.log(`🚶 Testing: ${testName}`);

    try {
      const phoneNumber = TEST_PHONE_NUMBERS.tanzania; // Swahili region
      const businessId = 'test_business_123';
      
      const { result, duration } = await TestUtils.measureExecutionTime(async () => {
        const journey: any[] = [];
        
        // Step 1: User contacts business (language detection)
        const context = TestUtils.createTestContext(phoneNumber);
        const detectedPreference = await multilingualApiMiddleware.detectLanguagePreference(context);
        journey.push({
          step: 'language_detection',
          detected: detectedPreference.language,
          confidence: detectedPreference.confidence,
          source: detectedPreference.source
        });
        
        // Step 2: Get business content in detected language
        const businessResponse = await businessApi.getByIdMultilingual(businessId, context);
        journey.push({
          step: 'business_content',
          hasData: !!businessResponse.data,
          language: businessResponse.requestedLanguage,
          hasTranslation: !!businessResponse.translation
        });
        
        // Step 3: Get products in user's language
        const productsResponse = await productsApi.getByBusinessIdMultilingual(businessId, context);
        journey.push({
          step: 'products_content',
          hasData: Array.isArray(productsResponse.data),
          language: productsResponse.requestedLanguage,
          hasTranslation: !!productsResponse.translation
        });
        
        // Step 4: Get FAQs in user's language
        const faqsResponse = await faqsApi.getByBusinessIdMultilingual(businessId, context);
        journey.push({
          step: 'faqs_content',
          hasData: Array.isArray(faqsResponse.data),
          language: faqsResponse.requestedLanguage,
          hasTranslation: !!faqsResponse.translation
        });
        
        // Step 5: Update user preference based on interaction
        await userLanguageService.recordLanguageDetection(
          phoneNumber,
          detectedPreference.language,
          detectedPreference.confidence
        );
        journey.push({
          step: 'preference_update',
          completed: true
        });
        
        return journey;
      });

      const completedSteps = result.filter(step => 
        step.hasData !== false && step.completed !== false && !step.error
      ).length;
      const expectedSteps = 5;
      
      return {
        name: testName,
        passed: completedSteps === expectedSteps,
        duration,
        metadata: {
          completedSteps,
          expectedSteps,
          journeyCompletionRate: (completedSteps / expectedSteps) * 100,
          journey: result
        }
      };

    } catch (error) {
      return {
        name: testName,
        passed: false,
        duration: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Run all end-to-end integration tests
   */
  static async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Running End-to-End Integration Test Suite');
    
    const tests = [
      this.testWhatsAppMessageTranslationWorkflow,
      this.testLanguageDetectionAccuracy,
      this.testAPIBackwardCompatibility,
      this.testMultilingualContentBundleRetrieval,
      this.testCostTrackingAndAnalyticsIntegration,
      this.testCompleteUserJourney
    ];

    const results: TestResult[] = [];
    
    for (const test of tests) {
      try {
        const result = await test();
        results.push(result);
      } catch (error) {
        results.push({
          name: 'Unknown Integration Test',
          passed: false,
          duration: 0,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return results;
  }
}
