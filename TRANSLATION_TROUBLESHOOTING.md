# AfriBot Translation Services - Troubleshooting Guide

## 🔧 **Translation System Troubleshooting Guide**

**Complete troubleshooting guide for AfriBot's multilingual translation system**

**Version**: 1.0.0  
**Last Updated**: December 10, 2024

---

## **📋 Table of Contents**

1. [Common Issues](#common-issues)
2. [Translation Quality Problems](#translation-quality-problems)
3. [Performance Issues](#performance-issues)
4. [Cost Management Issues](#cost-management-issues)
5. [API Integration Problems](#api-integration-problems)
6. [Database Issues](#database-issues)
7. [WhatsApp Integration Issues](#whatsapp-integration-issues)
8. [Monitoring & Debugging](#monitoring--debugging)
9. [Emergency Procedures](#emergency-procedures)
10. [Getting Help](#getting-help)

---

## **🚨 Common Issues**

### **Translation Not Working**

**Symptoms:**
- Messages not being translated
- API returns errors
- Original text returned instead of translation

**Diagnosis Steps:**
```bash
# 1. Check API key configuration
echo $VITE_GOOGLE_TRANSLATE_API_KEY | head -c 20

# 2. Test API connectivity
curl "https://translation.googleapis.com/language/translate/v2?key=$VITE_GOOGLE_TRANSLATE_API_KEY" \
  -d "q=hello&target=sw&source=en"

# 3. Check service status
curl https://api.afrobot.ai/api/translation-health

# 4. Verify database connectivity
curl https://api.afrobot.ai/api/db-health
```

**Common Solutions:**

**1. Invalid API Key:**
```bash
# Verify Google Cloud API key
gcloud auth application-default print-access-token

# Check API key permissions
gcloud projects get-iam-policy YOUR_PROJECT_ID
```

**2. Quota Exceeded:**
```bash
# Check current quota usage
gcloud logging read "resource.type=gce_instance AND jsonPayload.quota_exceeded=true" --limit=10

# Increase quota in Google Cloud Console
# Navigate to: APIs & Services > Quotas
```

**3. Network Connectivity:**
```bash
# Test external API connectivity
curl -I https://translation.googleapis.com

# Check firewall rules
iptables -L | grep 443
```

### **Language Detection Issues**

**Symptoms:**
- Wrong language detected
- Low confidence scores
- Inconsistent detection results

**Diagnosis:**
```javascript
// Test language detection
const testCases = [
  { text: "Habari za asubuhi", expected: "sw" },
  { text: "Sannu da zuwa", expected: "ha" },
  { text: "Bawo ni", expected: "yo" }
];

for (const test of testCases) {
  const result = await translationService.detectLanguage(test.text);
  console.log(`Text: "${test.text}"`);
  console.log(`Expected: ${test.expected}, Detected: ${result.language}`);
  console.log(`Confidence: ${result.confidence}`);
}
```

**Solutions:**

**1. Improve Detection Accuracy:**
```typescript
// Use phone number context for better detection
const context = {
  phoneNumber: "+254712345678", // Kenya - likely Swahili
  previousLanguage: "sw",
  conversationHistory: ["Habari", "Asante"]
};

const detection = await translationService.detectLanguage(text, context);
```

**2. Manual Override:**
```typescript
// Allow manual language selection
await userLanguageService.setUserLanguagePreference(phoneNumber, "sw");
```

### **Poor Translation Quality**

**Symptoms:**
- Translations don't make sense
- Cultural context lost
- Technical terms incorrectly translated

**Diagnosis:**
```typescript
// Check translation confidence scores
const result = await translationService.translateText(
  "Welcome to our electronics store",
  "sw"
);

console.log(`Confidence: ${result.confidence}`);
console.log(`Translation: ${result.translatedText}`);

if (result.confidence < 0.8) {
  console.warn("Low confidence translation detected");
}
```

**Solutions:**

**1. Enable Cultural Adaptation:**
```typescript
const culturalSettings = {
  formalityLevel: 'professional',
  culturalAdaptation: true,
  businessTone: 'friendly',
  preserveBrandNames: true
};

const result = await businessContentTranslationService.translateBusinessProfile(
  business,
  ['sw'],
  culturalSettings
);
```

**2. Use Business Context:**
```typescript
const context = {
  businessContext: {
    name: business.name,
    sector: business.sector,
    tone: 'professional'
  },
  conversationType: 'customer_service'
};

const translation = await translationService.translateText(
  text,
  targetLanguage,
  sourceLanguage,
  config,
  context
);
```

**3. Implement Translation Templates:**
```typescript
// Use pre-translated templates for common phrases
const templates = {
  'welcome': {
    'sw': 'Karibu!',
    'ha': 'Maraba!',
    'yo': 'Kaabo!'
  },
  'thank_you': {
    'sw': 'Asante sana',
    'ha': 'Na gode sosai',
    'yo': 'E se pupo'
  }
};
```

---

## **⚡ Performance Issues**

### **Slow Translation Response Times**

**Symptoms:**
- Translation takes >2 seconds
- Timeouts occurring
- Poor user experience

**Diagnosis:**
```bash
# Monitor translation response times
curl -w "@curl-format.txt" -X POST https://api.afrobot.ai/api/translate \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello", "targetLanguage": "sw"}'

# Check cache hit rates
redis-cli info stats | grep keyspace_hits
redis-cli info stats | grep keyspace_misses
```

**Solutions:**

**1. Optimize Caching:**
```typescript
// Increase cache TTL for frequently used translations
const cacheConfig = {
  translationTTL: 3600, // 1 hour instead of 30 minutes
  contentTTL: 7200, // 2 hours for business content
  templateTTL: 86400 // 24 hours for templates
};
```

**2. Implement Connection Pooling:**
```typescript
// Configure connection pooling for translation APIs
const httpAgent = new https.Agent({
  keepAlive: true,
  maxSockets: 50,
  maxFreeSockets: 10,
  timeout: 30000
});
```

**3. Use Batch Processing:**
```typescript
// Batch multiple translations together
const batchTranslation = await translationService.translateBatch([
  { text: "Hello", targetLanguage: "sw" },
  { text: "Thank you", targetLanguage: "sw" },
  { text: "Goodbye", targetLanguage: "sw" }
]);
```

### **High Memory Usage**

**Symptoms:**
- Server running out of memory
- Slow garbage collection
- Application crashes

**Diagnosis:**
```bash
# Monitor memory usage
free -h
ps aux --sort=-%mem | head -10

# Check Node.js memory usage
node --inspect app.js
# Then connect to Chrome DevTools
```

**Solutions:**

**1. Implement Memory Limits:**
```javascript
// Set Node.js memory limits
node --max-old-space-size=4096 app.js

// Implement cache size limits
const cacheConfig = {
  maxMemory: '1GB',
  evictionPolicy: 'allkeys-lru'
};
```

**2. Optimize Translation Cache:**
```typescript
// Implement cache cleanup
setInterval(() => {
  translationCache.cleanup();
}, 300000); // Clean up every 5 minutes
```

---

## **💰 Cost Management Issues**

### **Unexpected High Costs**

**Symptoms:**
- Translation costs exceeding budget
- Unexpected charges
- Cost alerts not working

**Diagnosis:**
```bash
# Check current month costs
curl https://api.afrobot.ai/api/analytics/translation/costs/business_123 \
  -H "Authorization: Bearer $TOKEN"

# Analyze cost by provider
curl https://api.afrobot.ai/api/analytics/costs/breakdown \
  -H "Authorization: Bearer $TOKEN"
```

**Solutions:**

**1. Implement Cost Controls:**
```typescript
// Set daily and monthly limits
const costLimits = {
  dailyLimit: 50, // $50 per day
  monthlyLimit: 1000, // $1000 per month
  alertThreshold: 0.8 // Alert at 80%
};

await costTrackingService.setCostLimits(businessId, costLimits);
```

**2. Optimize Cache Usage:**
```typescript
// Increase cache hit rate
const optimizations = {
  enableSmartCaching: true,
  cacheCommonPhrases: true,
  batchSimilarRequests: true,
  useTranslationTemplates: true
};
```

**3. Review Translation Patterns:**
```sql
-- Find most expensive translations
SELECT 
  source_language,
  target_language,
  COUNT(*) as translation_count,
  SUM(cost) as total_cost,
  AVG(cost) as avg_cost
FROM translation_analytics 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY source_language, target_language
ORDER BY total_cost DESC;
```

### **Cost Tracking Inaccurate**

**Symptoms:**
- Cost reports don't match provider bills
- Missing cost data
- Incorrect cost calculations

**Solutions:**

**1. Verify Cost Tracking:**
```typescript
// Ensure all translations are tracked
const trackTranslationCost = async (translation) => {
  await costTrackingService.trackTranslationCost({
    businessId: translation.businessId,
    provider: translation.provider,
    sourceLanguage: translation.sourceLanguage,
    targetLanguage: translation.targetLanguage,
    characterCount: translation.text.length,
    cost: calculateCost(translation)
  });
};
```

**2. Reconcile with Provider Bills:**
```bash
# Export cost data for reconciliation
curl https://api.afrobot.ai/api/analytics/costs/export \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"startDate": "2024-12-01", "endDate": "2024-12-31"}' \
  > costs_december_2024.csv
```

---

## **🔌 API Integration Problems**

### **Authentication Issues**

**Symptoms:**
- 401 Unauthorized errors
- Token expired messages
- Permission denied errors

**Solutions:**

**1. Verify API Keys:**
```bash
# Test API key validity
curl -H "Authorization: Bearer $API_TOKEN" \
  https://api.afrobot.ai/api/user/profile

# Check token expiration
jwt-decode $API_TOKEN
```

**2. Refresh Tokens:**
```typescript
// Implement automatic token refresh
const refreshToken = async () => {
  const response = await fetch('/api/auth/refresh', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${refreshToken}`
    }
  });
  
  if (response.ok) {
    const { accessToken } = await response.json();
    localStorage.setItem('accessToken', accessToken);
    return accessToken;
  }
  
  throw new Error('Token refresh failed');
};
```

### **Rate Limiting Issues**

**Symptoms:**
- 429 Too Many Requests errors
- Requests being throttled
- Slow API responses

**Solutions:**

**1. Implement Exponential Backoff:**
```typescript
const retryWithBackoff = async (fn, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      if (error.status === 429 && attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      throw error;
    }
  }
};
```

**2. Implement Request Queuing:**
```typescript
class RequestQueue {
  private queue: Array<() => Promise<any>> = [];
  private processing = false;
  private requestsPerMinute = 100;
  
  async add<T>(request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await request();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
      
      this.process();
    });
  }
  
  private async process() {
    if (this.processing || this.queue.length === 0) return;
    
    this.processing = true;
    const request = this.queue.shift()!;
    
    await request();
    
    // Rate limiting delay
    await new Promise(resolve => 
      setTimeout(resolve, 60000 / this.requestsPerMinute)
    );
    
    this.processing = false;
    this.process(); // Process next request
  }
}
```

---

## **🗄️ Database Issues**

### **Connection Problems**

**Symptoms:**
- Database connection timeouts
- Connection pool exhausted
- Slow query performance

**Solutions:**

**1. Optimize Connection Pool:**
```typescript
// Configure Supabase connection pool
const supabase = createClient(url, key, {
  db: {
    schema: 'public',
  },
  auth: {
    autoRefreshToken: true,
    persistSession: true,
  },
  global: {
    headers: {
      'x-connection-pool-size': '20'
    }
  }
});
```

**2. Implement Connection Retry:**
```typescript
const connectWithRetry = async (maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const { data, error } = await supabase
        .from('businesses')
        .select('id')
        .limit(1);
      
      if (!error) return true;
      
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, attempt * 1000));
        continue;
      }
      
      throw error;
    } catch (error) {
      if (attempt === maxRetries) throw error;
    }
  }
};
```

### **Query Performance Issues**

**Symptoms:**
- Slow database queries
- High CPU usage
- Query timeouts

**Solutions:**

**1. Add Missing Indexes:**
```sql
-- Add indexes for translation queries
CREATE INDEX CONCURRENTLY idx_translation_cache_lookup 
ON translation_cache(source_text_hash, source_language, target_language);

CREATE INDEX CONCURRENTLY idx_translation_analytics_business_date 
ON translation_analytics(business_id, created_at);

CREATE INDEX CONCURRENTLY idx_user_language_preferences_phone 
ON user_language_preferences(phone_number);
```

**2. Optimize Queries:**
```sql
-- Use EXPLAIN ANALYZE to identify slow queries
EXPLAIN ANALYZE 
SELECT * FROM translation_cache 
WHERE source_text_hash = 'hash123' 
AND source_language = 'en' 
AND target_language = 'sw';

-- Optimize with proper indexing and query structure
```

---

## **📱 WhatsApp Integration Issues**

### **Message Translation Not Working**

**Symptoms:**
- WhatsApp messages not being translated
- Translation delays in conversations
- Original language messages sent to customers

**Solutions:**

**1. Verify Webhook Configuration:**
```bash
# Test webhook endpoint
curl -X POST https://api.afrobot.ai/api/webhook/whatsapp \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{
      "from": "+254712345678",
      "text": {"body": "Habari za asubuhi"}
    }]
  }'
```

**2. Check Message Processing Pipeline:**
```typescript
// Debug message translation flow
const debugMessageTranslation = async (message) => {
  console.log('1. Received message:', message);
  
  const detection = await messageTranslationService.detectLanguage(message.text);
  console.log('2. Language detected:', detection);
  
  const translation = await messageTranslationService.translateIncomingMessage(
    message.text,
    'en',
    { phoneNumber: message.from }
  );
  console.log('3. Translation result:', translation);
};
```

---

## **📊 Monitoring & Debugging**

### **Enable Debug Logging**

```typescript
// Enable detailed logging
const logger = {
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: 'json',
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'translation.log' })
  ]
};

// Log translation requests
translationService.on('translate', (data) => {
  logger.debug('Translation request', {
    text: data.text.substring(0, 50),
    sourceLanguage: data.sourceLanguage,
    targetLanguage: data.targetLanguage,
    provider: data.provider
  });
});
```

### **Health Check Monitoring**

```bash
# Set up health check monitoring
#!/bin/bash
# health-check.sh

ENDPOINTS=(
  "https://api.afrobot.ai/health"
  "https://api.afrobot.ai/api/translation-health"
  "https://api.afrobot.ai/api/db-health"
)

for endpoint in "${ENDPOINTS[@]}"; do
  response=$(curl -s -o /dev/null -w "%{http_code}" "$endpoint")
  if [ "$response" != "200" ]; then
    echo "ALERT: $endpoint returned $response"
    # Send alert to monitoring system
  fi
done
```

---

## **🚨 Emergency Procedures**

### **Complete Translation System Failure**

**Immediate Actions:**
1. **Enable Fallback Mode:**
```bash
# Disable translation features
curl -X POST https://api.afrobot.ai/api/admin/feature-flags \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{"translation_enabled": false}'
```

2. **Notify Users:**
```typescript
// Send system status notification
const notification = {
  message: "Translation services temporarily unavailable. Operating in English-only mode.",
  severity: "warning",
  channels: ["dashboard", "email", "sms"]
};

await notificationService.broadcast(notification);
```

3. **Activate Monitoring:**
```bash
# Increase monitoring frequency
# Monitor system recovery
# Track error rates and response times
```

### **High Cost Alert Response**

**Immediate Actions:**
1. **Implement Cost Circuit Breaker:**
```typescript
const costCircuitBreaker = {
  dailyLimit: 500,
  monthlyLimit: 10000,
  action: 'pause_translations' // or 'alert_only'
};

if (currentDailyCost > costCircuitBreaker.dailyLimit) {
  await translationService.pause();
  await alertService.send('CRITICAL: Daily translation cost limit exceeded');
}
```

2. **Review Recent Activity:**
```sql
-- Check for unusual translation patterns
SELECT 
  business_id,
  COUNT(*) as translation_count,
  SUM(cost) as total_cost
FROM translation_analytics 
WHERE created_at >= NOW() - INTERVAL '1 hour'
GROUP BY business_id
ORDER BY total_cost DESC
LIMIT 10;
```

---

## **📞 Getting Help**

### **Self-Service Resources**

**Documentation:**
- [Technical Integration Guide](./TRANSLATION_INTEGRATION.md)
- [API Reference](./TRANSLATION_API_REFERENCE.md)
- [User Guide](./MULTILINGUAL_USER_GUIDE.md)
- [Deployment Guide](./TRANSLATION_DEPLOYMENT_GUIDE.md)

**Diagnostic Tools:**
- Health check endpoints
- Debug logging configuration
- Performance monitoring dashboards
- Cost tracking reports

### **Support Channels**

**Priority Support (Production Issues):**
- **Phone:** +254-XXX-XXXX (24/7)
- **Email:** <EMAIL>
- **Slack:** #afrobot-emergency

**General Support:**
- **Email:** <EMAIL>
- **Documentation:** https://docs.afrobot.ai
- **Community:** https://community.afrobot.ai
- **GitHub Issues:** https://github.com/afrobot/issues

**When Contacting Support, Include:**
1. Error messages and stack traces
2. Request/response examples
3. System configuration details
4. Steps to reproduce the issue
5. Business impact assessment

---

**🔧 This troubleshooting guide helps you quickly identify and resolve issues with AfriBot's translation system. For additional support, don't hesitate to contact our technical team!** 🚀
