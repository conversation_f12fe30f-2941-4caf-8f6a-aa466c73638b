# AfriBot Translation API Reference

## 🌍 **Translation API Reference Documentation**

**Complete API reference for AfriBot's multilingual translation system**

**Version**: 1.0.0  
**Base URL**: `https://api.afrobot.ai`  
**Last Updated**: December 10, 2024

---

## **📋 Table of Contents**

1. [Authentication](#authentication)
2. [Core Translation APIs](#core-translation-apis)
3. [Multilingual Business APIs](#multilingual-business-apis)
4. [Language Management APIs](#language-management-apis)
5. [Analytics & Monitoring APIs](#analytics--monitoring-apis)
6. [Bulk Translation APIs](#bulk-translation-apis)
7. [Error Handling](#error-handling)
8. [Rate Limiting](#rate-limiting)
9. [SDKs & Examples](#sdks--examples)

---

## **🔐 Authentication**

All API requests require authentication using Bearer tokens or API keys.

### **Authentication Methods**

**1. Bear<PERSON> (Recommended):**
```http
Authorization: Bearer your_jwt_token
```

**2. API Key:**
```http
X-API-Key: your_api_key
```

**3. Service Role (Internal):**
```http
Authorization: Bearer your_service_role_key
```

---

## **🔤 Core Translation APIs**

### **Translate Text**

Translate text between supported languages with cultural adaptation.

**Endpoint:** `POST /api/translate`

**Request:**
```json
{
  "text": "Welcome to our store!",
  "targetLanguage": "sw",
  "sourceLanguage": "en",
  "context": {
    "businessId": "business_123",
    "conversationType": "customer_service",
    "culturalAdaptation": true
  },
  "config": {
    "provider": "google",
    "timeout": 30000,
    "retries": 3
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "translatedText": "Karibu dukani mwetu!",
    "originalText": "Welcome to our store!",
    "sourceLanguage": "en",
    "targetLanguage": "sw",
    "confidence": 0.95,
    "provider": "google",
    "cached": false,
    "processingTime": 245,
    "cost": 0.002,
    "culturallyAdapted": true
  }
}
```

### **Detect Language**

Automatically detect the language of input text.

**Endpoint:** `POST /api/translate/detect`

**Request:**
```json
{
  "text": "Habari za asubuhi"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "language": "sw",
    "confidence": 0.98,
    "alternatives": [
      {"language": "sw", "confidence": 0.98},
      {"language": "en", "confidence": 0.02}
    ]
  }
}
```

### **Batch Translation**

Translate multiple texts in a single request for efficiency.

**Endpoint:** `POST /api/translate/batch`

**Request:**
```json
{
  "texts": [
    "Hello",
    "How are you?",
    "Thank you"
  ],
  "targetLanguage": "sw",
  "sourceLanguage": "en",
  "context": {
    "businessId": "business_123"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "translations": [
      {
        "originalText": "Hello",
        "translatedText": "Hujambo",
        "confidence": 0.96
      },
      {
        "originalText": "How are you?",
        "translatedText": "Hujambo?",
        "confidence": 0.94
      },
      {
        "originalText": "Thank you",
        "translatedText": "Asante",
        "confidence": 0.98
      }
    ],
    "totalCost": 0.006,
    "processingTime": 567
  }
}
```

---

## **🏢 Multilingual Business APIs**

### **Get Business (Multilingual)**

Retrieve business information with automatic translation.

**Endpoint:** `GET /api/businesses/{businessId}`

**Query Parameters:**
- `lang` (optional): Target language code
- `phoneNumber` (optional): User's phone number for language detection
- `autoDetect` (optional): Enable automatic language detection

**Request:**
```http
GET /api/businesses/business_123?lang=sw&phoneNumber=%2B254712345678
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "business_123",
    "name": "Mama Sarah's Shop",
    "description": "Tunauzwa mazao mazuri na vitu vya nyumbani",
    "greeting": "Karibu! Tunaweza kukusaidiaje?",
    "location": "Nairobi, Kenya",
    "phone": "+254712345678",
    "sector": "retail"
  },
  "translation": {
    "originalLanguage": "en",
    "targetLanguage": "sw",
    "confidence": 0.92,
    "provider": "google",
    "cached": true,
    "translatedAt": "2024-12-10T10:30:00Z",
    "processingTime": 45
  },
  "availableLanguages": ["en", "sw", "ha", "yo", "fr"],
  "requestedLanguage": "sw"
}
```

### **Get Products (Multilingual)**

Retrieve business products with translation.

**Endpoint:** `GET /api/businesses/{businessId}/products`

**Query Parameters:**
- `lang` (optional): Target language code
- `category` (optional): Product category filter
- `limit` (optional): Number of products to return
- `offset` (optional): Pagination offset

**Request:**
```http
GET /api/businesses/business_123/products?lang=ha&limit=10
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "product_1",
      "name": "Tumatir mai kyau",
      "description": "Tumatir da aka girka a gida, mai kyau don dafa abinci",
      "price": 50,
      "currency": "KES",
      "category": "vegetables",
      "inStock": true
    }
  ],
  "translation": {
    "originalLanguage": "en",
    "targetLanguage": "ha",
    "confidence": 0.89,
    "provider": "google",
    "cached": false,
    "processingTime": 123
  },
  "pagination": {
    "total": 25,
    "limit": 10,
    "offset": 0,
    "hasMore": true
  }
}
```

### **Get FAQs (Multilingual)**

Retrieve frequently asked questions with translation.

**Endpoint:** `GET /api/businesses/{businessId}/faqs`

**Query Parameters:**
- `lang` (optional): Target language code
- `category` (optional): FAQ category filter

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "faq_1",
      "question": "Je, mnauzwa mazao mapya?",
      "answer": "Ndiyo, tunauzwa mazao mapya kila siku. Tunapata mazao kutoka kwa wakulima wa hapa.",
      "category": "products"
    }
  ],
  "translation": {
    "originalLanguage": "en",
    "targetLanguage": "sw",
    "confidence": 0.94,
    "provider": "google"
  }
}
```

### **Get Complete Business Bundle**

Get all business content (business info, products, FAQs) in one request.

**Endpoint:** `GET /api/businesses/{businessId}/bundle`

**Query Parameters:**
- `lang` (optional): Target language code
- `phoneNumber` (optional): User's phone number
- `includeAnalytics` (optional): Include language analytics

**Response:**
```json
{
  "success": true,
  "data": {
    "business": { /* Business object */ },
    "products": [ /* Product array */ ],
    "faqs": [ /* FAQ array */ ],
    "languagePreference": {
      "phoneNumber": "+254712345678",
      "preferredLanguage": "sw",
      "confidence": 0.95,
      "source": "phone_region",
      "manuallySet": false
    }
  },
  "translation": {
    "originalLanguage": "en",
    "targetLanguage": "sw",
    "totalItems": 15,
    "averageConfidence": 0.91,
    "totalCost": 0.045,
    "processingTime": 1234
  }
}
```

---

## **🗣️ Language Management APIs**

### **Get User Language Preference**

Retrieve language preference for a user.

**Endpoint:** `GET /api/language/preference/{phoneNumber}`

**Response:**
```json
{
  "success": true,
  "data": {
    "phoneNumber": "+254712345678",
    "preferredLanguage": "sw",
    "detectedLanguage": "sw",
    "confidence": 0.95,
    "source": "phone_region",
    "manuallySet": false,
    "lastUpdated": "2024-12-10T10:30:00Z",
    "availableLanguages": ["en", "sw", "ha", "yo", "fr", "ar", "pt", "am"]
  }
}
```

### **Set User Language Preference**

Set or update user's language preference.

**Endpoint:** `POST /api/language/preference`

**Request:**
```json
{
  "phoneNumber": "+254712345678",
  "language": "sw",
  "manuallySet": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "phoneNumber": "+254712345678",
    "preferredLanguage": "sw",
    "manuallySet": true,
    "updatedAt": "2024-12-10T10:30:00Z"
  }
}
```

### **Detect Language from Phone Number**

Detect likely language based on phone number region.

**Endpoint:** `POST /api/language/detect-from-phone`

**Request:**
```json
{
  "phoneNumber": "+254712345678"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "phoneNumber": "+254712345678",
    "country": "Kenya",
    "countryCode": "KE",
    "likelyLanguages": [
      {"language": "sw", "confidence": 0.7},
      {"language": "en", "confidence": 0.3}
    ],
    "primaryLanguage": "sw"
  }
}
```

---

## **📊 Analytics & Monitoring APIs**

### **Get Translation Metrics**

Retrieve comprehensive translation metrics.

**Endpoint:** `GET /api/analytics/translation/metrics`

**Query Parameters:**
- `businessId` (optional): Filter by business
- `startDate` (required): Start date (ISO 8601)
- `endDate` (required): End date (ISO 8601)
- `groupBy` (optional): Group by 'day', 'week', 'month'

**Response:**
```json
{
  "success": true,
  "data": {
    "totalTranslations": 15420,
    "successfulTranslations": 14891,
    "failedTranslations": 529,
    "averageConfidence": 0.87,
    "averageProcessingTime": 1250,
    "totalCost": 234.56,
    "costPerTranslation": 0.0152,
    "cacheHitRate": 68.3,
    "throughputPerHour": 1284,
    "languageDistribution": {
      "sw": 45.2,
      "ha": 23.1,
      "yo": 18.7,
      "fr": 13.0
    }
  }
}
```

### **Get Language Usage Analytics**

Get detailed language usage statistics.

**Endpoint:** `GET /api/analytics/language/usage/{businessId}`

**Response:**
```json
{
  "success": true,
  "data": {
    "languageUsage": [
      {
        "language": "sw",
        "messageCount": 1250,
        "userCount": 89,
        "percentage": 45.2
      },
      {
        "language": "ha",
        "messageCount": 890,
        "userCount": 67,
        "percentage": 32.1
      }
    ],
    "translationMetrics": {
      "totalTranslations": 15420,
      "totalCost": 234.56,
      "averageConfidence": 0.87,
      "cacheHitRate": 68.3
    },
    "userEngagement": {
      "multilingualUsers": 156,
      "languageSwitches": 23,
      "preferredLanguages": ["sw", "ha", "yo"]
    }
  }
}
```

### **Get Cost Analysis**

Retrieve detailed cost analysis and optimization insights.

**Endpoint:** `GET /api/analytics/translation/costs/{businessId}`

**Query Parameters:**
- `startDate` (required): Start date
- `endDate` (required): End date
- `groupBy` (optional): 'day', 'week', 'month'

**Response:**
```json
{
  "success": true,
  "data": {
    "totalCost": 234.56,
    "costByProvider": {
      "google": 189.23,
      "azure": 32.45,
      "aws": 12.88
    },
    "costByLanguage": [
      {"language": "sw", "cost": 89.23},
      {"language": "ha", "cost": 67.45},
      {"language": "yo", "cost": 45.67}
    ],
    "costTrends": [
      {"date": "2024-12-01", "cost": 23.45},
      {"date": "2024-12-02", "cost": 28.67}
    ],
    "optimizationSavings": {
      "caching": 156.78,
      "batching": 45.23,
      "templates": 67.89
    },
    "projectedMonthlyCost": 1234.56
  }
}
```

---

## **🔄 Bulk Translation APIs**

### **Start Bulk Translation Job**

Initiate bulk translation for business content.

**Endpoint:** `POST /api/translation/bulk`

**Request:**
```json
{
  "businessId": "business_123",
  "contentIds": ["product_1", "product_2", "faq_1"],
  "contentType": "product",
  "targetLanguages": ["sw", "ha", "yo"],
  "priority": "normal",
  "notifyOnComplete": true,
  "webhookUrl": "https://your-app.com/webhook/translation-complete"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "jobId": "job_abc123",
    "status": "queued",
    "progress": {
      "total": 9,
      "completed": 0,
      "failed": 0,
      "estimatedCost": 12.45,
      "estimatedTime": 15
    },
    "createdAt": "2024-12-10T10:30:00Z"
  }
}
```

### **Get Bulk Translation Status**

Check the status of a bulk translation job.

**Endpoint:** `GET /api/translation/bulk/{jobId}`

**Response:**
```json
{
  "success": true,
  "data": {
    "jobId": "job_abc123",
    "status": "in_progress",
    "progress": {
      "total": 9,
      "completed": 6,
      "failed": 0,
      "estimatedCost": 12.45,
      "actualCost": 8.23,
      "estimatedTime": 15,
      "elapsedTime": 8
    },
    "results": [
      {
        "contentId": "product_1",
        "status": "completed",
        "translations": {
          "sw": {"confidence": 0.92, "cost": 0.02},
          "ha": {"confidence": 0.89, "cost": 0.02}
        }
      }
    ],
    "createdAt": "2024-12-10T10:30:00Z",
    "updatedAt": "2024-12-10T10:38:00Z"
  }
}
```

### **Cancel Bulk Translation Job**

Cancel a running bulk translation job.

**Endpoint:** `DELETE /api/translation/bulk/{jobId}`

**Response:**
```json
{
  "success": true,
  "data": {
    "jobId": "job_abc123",
    "status": "cancelled",
    "completedItems": 3,
    "refundAmount": 4.22
  }
}
```

---

## **🚨 Error Handling**

### **Error Response Format**

All API errors follow a consistent format:

```json
{
  "success": false,
  "error": {
    "code": "TRANSLATION_FAILED",
    "message": "Translation service temporarily unavailable",
    "details": {
      "provider": "google",
      "retryAfter": 30,
      "supportId": "err_abc123"
    }
  },
  "timestamp": "2024-12-10T10:30:00Z"
}
```

### **Error Codes**

| Code | HTTP Status | Description | Retry |
|------|-------------|-------------|-------|
| `INVALID_REQUEST` | 400 | Invalid request parameters | No |
| `UNAUTHORIZED` | 401 | Invalid or missing authentication | No |
| `FORBIDDEN` | 403 | Insufficient permissions | No |
| `NOT_FOUND` | 404 | Resource not found | No |
| `RATE_LIMITED` | 429 | Rate limit exceeded | Yes |
| `TRANSLATION_FAILED` | 500 | Translation service error | Yes |
| `PROVIDER_UNAVAILABLE` | 503 | Translation provider down | Yes |
| `QUOTA_EXCEEDED` | 429 | Translation quota exceeded | No |
| `LANGUAGE_NOT_SUPPORTED` | 400 | Unsupported language pair | No |
| `TEXT_TOO_LONG` | 400 | Text exceeds maximum length | No |
| `CONFIDENCE_TOO_LOW` | 422 | Translation confidence below threshold | No |

### **Error Handling Best Practices**

**1. Implement Retry Logic:**
```javascript
async function translateWithRetry(text, targetLang, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch('/api/translate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text, targetLanguage: targetLang })
      });

      if (response.ok) {
        return await response.json();
      }

      if (response.status === 429) {
        // Rate limited - wait and retry
        const retryAfter = response.headers.get('Retry-After') || 1;
        await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
        continue;
      }

      if (response.status >= 500 && attempt < maxRetries) {
        // Server error - retry with exponential backoff
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        continue;
      }

      throw new Error(`Translation failed: ${response.status}`);
    } catch (error) {
      if (attempt === maxRetries) throw error;
    }
  }
}
```

**2. Handle Graceful Degradation:**
```javascript
async function translateWithFallback(text, targetLang) {
  try {
    return await translateWithRetry(text, targetLang);
  } catch (error) {
    console.warn('Translation failed, using fallback:', error);

    // Fallback strategies:
    // 1. Return original text
    // 2. Use cached translation if available
    // 3. Use pre-translated templates

    return {
      success: false,
      data: {
        translatedText: text, // Original text as fallback
        originalText: text,
        confidence: 0,
        fallback: true
      }
    };
  }
}
```

---

## **⚡ Rate Limiting**

### **Rate Limits**

| Endpoint Category | Limit | Window | Burst |
|------------------|-------|---------|-------|
| Translation APIs | 100 req/min | 1 minute | 10 req/sec |
| Business APIs | 200 req/min | 1 minute | 20 req/sec |
| Analytics APIs | 50 req/min | 1 minute | 5 req/sec |
| Bulk Operations | 10 req/hour | 1 hour | 2 req/min |

### **Rate Limit Headers**

All responses include rate limit information:

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1702123456
X-RateLimit-Retry-After: 60
```

### **Rate Limit Handling**

```javascript
function handleRateLimit(response) {
  if (response.status === 429) {
    const retryAfter = response.headers.get('X-RateLimit-Retry-After');
    const resetTime = response.headers.get('X-RateLimit-Reset');

    console.log(`Rate limited. Retry after ${retryAfter} seconds`);
    console.log(`Rate limit resets at ${new Date(resetTime * 1000)}`);

    // Implement exponential backoff
    return new Promise(resolve => {
      setTimeout(resolve, retryAfter * 1000);
    });
  }
}
```

---

## **💻 SDKs & Examples**

### **JavaScript/TypeScript SDK**

**Installation:**
```bash
npm install @afrobot/translation-sdk
```

**Usage:**
```typescript
import { AfrobotTranslation } from '@afrobot/translation-sdk';

const client = new AfrobotTranslation({
  apiKey: 'your_api_key',
  baseUrl: 'https://api.afrobot.ai'
});

// Basic translation
const result = await client.translate({
  text: 'Hello world',
  targetLanguage: 'sw'
});

// Business content translation
const business = await client.getBusiness('business_123', {
  language: 'sw',
  phoneNumber: '+254712345678'
});

// Bulk translation
const job = await client.startBulkTranslation({
  businessId: 'business_123',
  contentType: 'product',
  targetLanguages: ['sw', 'ha', 'yo']
});
```

### **Python SDK**

**Installation:**
```bash
pip install afrobot-translation
```

**Usage:**
```python
from afrobot_translation import AfrobotClient

client = AfrobotClient(api_key='your_api_key')

# Basic translation
result = client.translate(
    text='Hello world',
    target_language='sw'
)

# Get business with translation
business = client.get_business(
    business_id='business_123',
    language='sw'
)
```

### **cURL Examples**

**Basic Translation:**
```bash
curl -X POST https://api.afrobot.ai/api/translate \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Welcome to our store!",
    "targetLanguage": "sw"
  }'
```

**Get Business (Multilingual):**
```bash
curl -X GET "https://api.afrobot.ai/api/businesses/business_123?lang=sw" \
  -H "Authorization: Bearer your_token"
```

**Start Bulk Translation:**
```bash
curl -X POST https://api.afrobot.ai/api/translation/bulk \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "businessId": "business_123",
    "contentIds": ["product_1", "product_2"],
    "contentType": "product",
    "targetLanguages": ["sw", "ha"]
  }'
```

### **Webhook Integration**

**Webhook Payload for Bulk Translation Complete:**
```json
{
  "event": "bulk_translation.completed",
  "jobId": "job_abc123",
  "businessId": "business_123",
  "status": "completed",
  "results": {
    "total": 10,
    "completed": 10,
    "failed": 0,
    "totalCost": 15.67
  },
  "timestamp": "2024-12-10T10:30:00Z"
}
```

**Webhook Verification:**
```javascript
const crypto = require('crypto');

function verifyWebhook(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');

  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}
```

---

## **🔧 Testing & Development**

### **Sandbox Environment**

**Base URL:** `https://api-sandbox.afrobot.ai`

**Test API Keys:**
- Use `test_` prefix for all test API keys
- Sandbox environment uses mock translation providers
- No charges applied for sandbox usage

### **Test Data**

**Sample Business ID:** `test_business_123`
**Sample Phone Numbers:**
- `+************` (Kenya - Swahili)
- `+************` (Nigeria - Hausa)
- `+************` (Ghana - English)

### **Mock Responses**

Enable mock responses for testing:
```http
X-Mock-Response: true
```

---

## **📚 Additional Resources**

### **Documentation Links**
- [Technical Integration Guide](./TRANSLATION_INTEGRATION.md)
- [User Guide](./MULTILINGUAL_USER_GUIDE.md)
- [Deployment Guide](./TRANSLATION_DEPLOYMENT_GUIDE.md)

### **Support Channels**
- **Email:** <EMAIL>
- **Documentation:** https://docs.afrobot.ai
- **Status Page:** https://status.afrobot.ai
- **GitHub:** https://github.com/afrobot/translation-api

### **Community**
- **Discord:** https://discord.gg/afrobot
- **Stack Overflow:** Tag `afrobot-translation`
- **Twitter:** @AfrobotAPI

---

**🌍 Ready to integrate AfriBot's multilingual translation capabilities? Start building with our comprehensive API today!** 🚀
```
