/**
 * Mock Services for Testing
 * Provides mock implementations of translation and API services for testing
 */

import type { SupportedLanguage } from '../types/translation';

// Mock translation service
export const mockTranslationService = {
  async translateText(
    text: string,
    sourceLanguage: SupportedLanguage,
    targetLanguage: SupportedLanguage
  ) {
    // Simulate translation delay
    await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 100));
    
    // Mock translations for common phrases
    const mockTranslations: Record<string, Record<string, string>> = {
      'Hello, welcome to our restaurant!': {
        sw: '<PERSON><PERSON><PERSON>, karibu kwenye mkahawa wetu!',
        ha: '<PERSON><PERSON>, maraba zuwa gidan abincinmu!',
        yo: '<PERSON><PERSON>, kaabo si ile ounjẹ wa!',
        fr: 'Bonjour, bienvenue dans notre restaurant!',
        ar: 'مرحبا، أهلا بكم في مطعمنا!',
        pt: 'Olá, bem-vindos ao nosso restaurante!',
        am: 'ሰላም፣ ወደ ምግብ ቤታችን እንኳን በደህና መጡ!'
      },
      'Thank you for your business!': {
        sw: 'Asante kwa biashara yako!',
        ha: 'Na gode da kasuwancin ku!',
        yo: 'E se fun iṣowo rẹ!',
        fr: 'Merci pour votre entreprise!',
        ar: 'شكرا لك على عملك!',
        pt: 'Obrigado pelo seu negócio!',
        am: 'ለንግድዎ እናመሰግናለን!'
      }
    };

    const translated = mockTranslations[text]?.[targetLanguage] || `[MOCK TRANSLATION: ${text}]`;
    
    return {
      translatedText: translated,
      confidence: Math.random() * 0.2 + 0.8, // 80-100% confidence
      provider: 'mock',
      sourceLanguage,
      targetLanguage,
      originalText: text,
      cached: Math.random() > 0.3 // 70% cache hit rate
    };
  },

  async detectLanguage(text: string) {
    // Simple language detection based on common words
    const languagePatterns = {
      sw: ['habari', 'asante', 'karibu', 'hujambo'],
      ha: ['sannu', 'na gode', 'maraba', 'yaya'],
      yo: ['bawo', 'e se', 'kaabo', 'pele'],
      fr: ['bonjour', 'merci', 'bienvenue', 'comment'],
      ar: ['مرحبا', 'شكرا', 'أهلا', 'كيف'],
      pt: ['olá', 'obrigado', 'bem-vindo', 'como'],
      am: ['ሰላም', 'አመሰግናለሁ', 'እንኳን', 'እንዴት']
    };

    const lowerText = text.toLowerCase();
    
    for (const [lang, patterns] of Object.entries(languagePatterns)) {
      if (patterns.some(pattern => lowerText.includes(pattern))) {
        return {
          language: lang as SupportedLanguage,
          confidence: Math.random() * 0.2 + 0.8
        };
      }
    }

    return {
      language: 'en' as SupportedLanguage,
      confidence: 0.9
    };
  }
};

// Mock business content translation service
export const mockBusinessContentTranslationService = {
  async translateBusinessProfile(business: any, languages: SupportedLanguage[]) {
    const translations: Record<string, any> = {};
    
    for (const language of languages) {
      const translation = await mockTranslationService.translateText(
        business.name,
        'en',
        language
      );
      
      translations[language] = {
        name: translation.translatedText,
        description: `[MOCK TRANSLATION: ${business.description}]`,
        greeting: `[MOCK TRANSLATION: ${business.greeting}]`,
        confidence: translation.confidence
      };
    }

    return {
      originalBusiness: business,
      translations,
      translationMetadata: {
        timestamp: new Date().toISOString(),
        provider: 'mock',
        totalCost: 0.001 * languages.length
      }
    };
  },

  async translateProduct(product: any, _business: any, languages: SupportedLanguage[]) {
    const translations: Record<string, any> = {};
    
    for (const language of languages) {
      translations[language] = {
        name: `[MOCK TRANSLATION: ${product.name}]`,
        description: `[MOCK TRANSLATION: ${product.description}]`,
        confidence: Math.random() * 0.2 + 0.8
      };
    }

    return {
      originalProduct: product,
      translations,
      translationMetadata: {
        timestamp: new Date().toISOString(),
        provider: 'mock',
        totalCost: 0.001 * languages.length
      }
    };
  },

  async translateFAQ(faq: any, _business: any, languages: SupportedLanguage[]) {
    const translations: Record<string, any> = {};
    
    for (const language of languages) {
      translations[language] = {
        question: `[MOCK TRANSLATION: ${faq.question}]`,
        answer: `[MOCK TRANSLATION: ${faq.answer}]`,
        confidence: Math.random() * 0.2 + 0.8
      };
    }

    return {
      originalFAQ: faq,
      translations,
      translationMetadata: {
        timestamp: new Date().toISOString(),
        provider: 'mock',
        totalCost: 0.001 * languages.length
      }
    };
  }
};

// Mock cultural adaptation service
export const mockCulturalAdaptationService = {
  async adaptTranslation(
    text: string,
    _sourceLanguage: SupportedLanguage,
    _targetLanguage: SupportedLanguage,
    _context: any
  ) {
    // Simulate cultural adaptation
    await new Promise(resolve => setTimeout(resolve, Math.random() * 300 + 50));
    
    return {
      adaptedText: `[CULTURALLY ADAPTED: ${text}]`,
      confidence: Math.random() * 0.2 + 0.8,
      culturalElements: ['greeting_style', 'formality_level', 'regional_terms'],
      adaptationReason: 'Applied cultural context for business communication'
    };
  }
};

// Mock multilingual API middleware
export const mockMultilingualApiMiddleware = {
  async detectLanguagePreference(context: any) {
    // Mock language detection based on phone number
    const phoneRegionMap: Record<string, SupportedLanguage> = {
      '+254': 'sw', // Kenya
      '+255': 'sw', // Tanzania
      '+234': 'ha', // Nigeria
      '+251': 'am', // Ethiopia
      '+212': 'ar', // Morocco
      '+221': 'fr', // Senegal
      '+244': 'pt', // Angola
      '+233': 'en'  // Ghana
    };

    const phonePrefix = context.phoneNumber?.substring(0, 4);
    const detectedLanguage = phoneRegionMap[phonePrefix] || 'en';
    
    return {
      language: detectedLanguage,
      confidence: 0.9,
      source: 'phone_region',
      phoneNumber: context.phoneNumber
    };
  }
};

// Mock API services
export const mockBusinessApi = {
  async getById(id: string) {
    return {
      id,
      name: 'Test Business',
      description: 'A test business for testing purposes',
      greeting: 'Welcome to our test business!'
    };
  },

  async getByIdMultilingual(id: string, context: any) {
    const business = await this.getById(id);
    const languagePreference = await mockMultilingualApiMiddleware.detectLanguagePreference(context);
    
    return {
      data: business,
      requestedLanguage: languagePreference.language,
      translation: languagePreference.language !== 'en' ? {
        provider: 'mock',
        confidence: 0.9,
        cached: false
      } : null,
      availableLanguages: ['en', 'sw', 'ha', 'yo', 'fr', 'ar', 'pt', 'am']
    };
  }
};

export const mockProductsApi = {
  async getByBusinessId(businessId: string) {
    return [
      {
        id: 'product_1',
        business_id: businessId,
        name: 'Test Product 1',
        description: 'A test product for testing',
        price: 100
      },
      {
        id: 'product_2',
        business_id: businessId,
        name: 'Test Product 2',
        description: 'Another test product',
        price: 200
      }
    ];
  },

  async getByBusinessIdMultilingual(businessId: string, context: any) {
    const products = await this.getByBusinessId(businessId);
    const languagePreference = await mockMultilingualApiMiddleware.detectLanguagePreference(context);
    
    return {
      data: products,
      requestedLanguage: languagePreference.language,
      translation: languagePreference.language !== 'en' ? {
        provider: 'mock',
        confidence: 0.9,
        cached: false
      } : null,
      availableLanguages: ['en', 'sw', 'ha', 'yo', 'fr', 'ar', 'pt', 'am']
    };
  }
};

export const mockFaqsApi = {
  async getByBusinessId(businessId: string) {
    return [
      {
        id: 'faq_1',
        business_id: businessId,
        question: 'What are your hours?',
        answer: 'We are open 24/7 for testing purposes'
      }
    ];
  },

  async getByBusinessIdMultilingual(businessId: string, context: any) {
    const faqs = await this.getByBusinessId(businessId);
    const languagePreference = await mockMultilingualApiMiddleware.detectLanguagePreference(context);
    
    return {
      data: faqs,
      requestedLanguage: languagePreference.language,
      translation: languagePreference.language !== 'en' ? {
        provider: 'mock',
        confidence: 0.9,
        cached: false
      } : null,
      availableLanguages: ['en', 'sw', 'ha', 'yo', 'fr', 'ar', 'pt', 'am']
    };
  }
};

// Mock user language service
export const mockUserLanguageService = {
  async recordLanguageDetection(_phoneNumber: string, _language: SupportedLanguage, _confidence: number) {
    return true;
  },

  async getUserLanguagePreference(phoneNumber: string) {
    return {
      phoneNumber,
      preferredLanguage: 'sw' as SupportedLanguage,
      confidence: 0.9,
      manuallySet: false,
      lastUpdated: new Date().toISOString()
    };
  }
};

// Mock message translation service
export const mockMessageTranslationService = {
  async detectLanguage(message: string) {
    return mockTranslationService.detectLanguage(message);
  },

  async translateMessage(
    message: string,
    sourceLanguage: SupportedLanguage,
    targetLanguage: SupportedLanguage,
    _context: any
  ) {
    return mockTranslationService.translateText(message, sourceLanguage, targetLanguage);
  }
};
