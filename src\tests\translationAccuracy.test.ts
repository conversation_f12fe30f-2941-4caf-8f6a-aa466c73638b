/**
 * Translation Accuracy Testing Suite
 * Comprehensive tests for translation quality validation across all supported African languages
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { TestUtils, TEST_CONFIG, TEST_LANGUAGES } from './setup';
import { translationService } from '../services/translationService';
import { businessContentTranslationService } from '../services/businessContentTranslationService';
import { culturalAdaptationService } from '../services/culturalAdaptationService';
import type { SupportedLanguage } from '../types/translation';

describe('Translation Accuracy Tests', () => {
  beforeAll(async () => {
    console.log('🧪 Setting up Translation Accuracy Test Suite');
  });

  afterAll(async () => {
    console.log('✅ Translation Accuracy Test Suite completed');
  });

  it('should translate text to all supported African languages with high confidence', async () => {
    const { result, duration } = await TestUtils.measureExecutionTime(async () => {
      const results: Record<string, any> = {};

      for (const language of TEST_LANGUAGES) {
        if (language === 'en') continue; // Skip English as source

        try {
          const translation = await translationService.translateText(
            'Hello, welcome to our restaurant!',
            'en',
            language
          );

          results[language] = {
            text: translation.translatedText,
            confidence: translation.confidence,
            provider: translation.provider
          };

          // Validate confidence threshold
          expect(translation.confidence).toBeGreaterThanOrEqual(TEST_CONFIG.confidenceThreshold);

        } catch (error) {
          results[language] = { error: error instanceof Error ? error.message : String(error) };
        }
      }

      return results;
    });

    // Validate response time
    expect(duration).toBeLessThanOrEqual(TEST_CONFIG.performanceThreshold);

    // Check if all languages were translated successfully
    const successfulTranslations = Object.values(result).filter(r => !r.error).length;
    const expectedTranslations = TEST_LANGUAGES.length - 1; // Excluding English

    expect(successfulTranslations).toBe(expectedTranslations);
  }, 30000);

  it('should translate business profiles accurately across multiple languages', async () => {
    const mockBusiness = TestUtils.createMockBusiness();

    const { result, duration } = await TestUtils.measureExecutionTime(async () => {
      const results: Record<string, any> = {};

      for (const language of ['sw', 'ha', 'fr'] as SupportedLanguage[]) {
        try {
          const translation = await businessContentTranslationService.translateBusinessProfile(
            mockBusiness,
            [language]
          );

          const langTranslation = translation.translations[language];
          results[language] = {
            name: langTranslation.name,
            description: langTranslation.description,
            greeting: langTranslation.greeting,
            confidence: langTranslation.confidence
          };

          // Validate confidence
          expect(langTranslation.confidence).toBeGreaterThanOrEqual(TEST_CONFIG.confidenceThreshold);

        } catch (error) {
          results[language] = { error: error instanceof Error ? error.message : String(error) };
        }
      }

      return results;
    });

    // Validate response time
    expect(duration).toBeLessThanOrEqual(TEST_CONFIG.performanceThreshold);

    // Check translation quality
    const successfulTranslations = Object.values(result).filter(r => !r.error).length;
    expect(successfulTranslations).toBe(3); // sw, ha, fr
  }, 30000);

  it('should perform cultural adaptation accurately', async () => {
    const { result, duration } = await TestUtils.measureExecutionTime(async () => {
      const results: Record<string, any> = {};

      const testMessages = [
        'Thank you for your business!',
        'We appreciate your feedback.',
        'Please let us know if you need help.'
      ];

      for (const language of ['sw', 'ha', 'yo'] as SupportedLanguage[]) {
        try {
          const adaptations = [];

          for (const message of testMessages) {
            const adaptation = await culturalAdaptationService.adaptTranslation(
              message,
              'en',
              language,
              {
                businessTone: 'professional',
                culturalContext: 'business',
                region: 'east_africa'
              }
            );

            adaptations.push({
              original: message,
              adapted: adaptation.adaptedText,
              confidence: adaptation.confidence,
              culturalElements: adaptation.culturalElements
            });
          }

          results[language] = adaptations;

        } catch (error) {
          results[language] = { error: error instanceof Error ? error.message : String(error) };
        }
      }

      return results;
    });

    // Validate response time
    expect(duration).toBeLessThanOrEqual(TEST_CONFIG.performanceThreshold);

    // Check adaptation quality
    const successfulAdaptations = Object.values(result).filter(r => !r.error).length;
    expect(successfulAdaptations).toBe(3); // sw, ha, yo
  }, 30000);

  it('should handle edge cases and special characters', async () => {
    const { result, duration } = await TestUtils.measureExecutionTime(async () => {
      const edgeCases = [
        '', // Empty string
        '   ', // Whitespace only
        'Hello! @#$%^&*()_+{}|:"<>?[]\\;\',./', // Special characters
        '123456789', // Numbers only
        'Hello\nWorld\tTest', // Newlines and tabs
        'Email: <EMAIL>, Phone: +254712345678', // Mixed content
        'Very long text that exceeds normal message length and contains multiple sentences with various punctuation marks, numbers like 123, and special characters like @#$%^&*()!', // Long text
        'Swahili: Habari, English: Hello, French: Bonjour' // Mixed languages
      ];

      const results: Record<string, any> = {};

      for (const testCase of edgeCases) {
        try {
          const translation = await translationService.translateText(
            testCase,
            'en',
            'sw'
          );

          results[testCase.substring(0, 20) + '...'] = {
            input: testCase,
            output: translation.translatedText,
            confidence: translation.confidence,
            handled: true
          };

        } catch (error) {
          results[testCase.substring(0, 20) + '...'] = {
            input: testCase,
            error: error instanceof Error ? error.message : String(error),
            handled: false
          };
        }
      }

      return results;
    });

    // Validate response time
    expect(duration).toBeLessThanOrEqual(TEST_CONFIG.performanceThreshold);

    // Check how many edge cases were handled gracefully
    const handledCases = Object.values(result).filter(r => r.handled || r.error?.includes('Empty')).length;
    const totalCases = Object.keys(result).length;

    expect(handledCases).toBeGreaterThanOrEqual(totalCases * 0.8); // 80% success rate for edge cases
  }, 30000);

});
