/**
 * Main Test Runner for AfriBot Translation Services
 * Orchestrates all test suites and generates comprehensive reports
 */

import { testRunner, TestSuite } from './setup';
import { TranslationAccuracyTests } from './translationAccuracy.test';
import { PerformanceValidationTests } from './performanceValidation.test';
import { EndToEndIntegrationTests } from './endToEndIntegration.test';
import { ErrorHandlingFallbackTests } from './errorHandlingFallback.test';

/**
 * Main Test Runner Class
 */
export class AfribotTestRunner {
  private startTime: number = 0;
  private endTime: number = 0;
  private allResults: TestSuite[] = [];

  /**
   * Run all test suites
   */
  async runAllTests(): Promise<void> {
    console.log('\n🚀 Starting AfriBot Translation Services Test Suite');
    console.log('====================================================');
    
    this.startTime = Date.now();

    try {
      // Run Translation Accuracy Tests
      console.log('\n📋 Phase 1: Translation Accuracy Testing');
      const accuracyResults = await TranslationAccuracyTests.runAllTests();
      const accuracySuite = await testRunner.runSuite('Translation Accuracy', 
        accuracyResults.map(result => async () => result)
      );
      this.allResults.push(accuracySuite);

      // Run Performance Validation Tests
      console.log('\n📋 Phase 2: Performance Validation Testing');
      const performanceResults = await PerformanceValidationTests.runAllTests();
      const performanceSuite = await testRunner.runSuite('Performance Validation', 
        performanceResults.map(result => async () => result)
      );
      this.allResults.push(performanceSuite);

      // Run End-to-End Integration Tests
      console.log('\n📋 Phase 3: End-to-End Integration Testing');
      const integrationResults = await EndToEndIntegrationTests.runAllTests();
      const integrationSuite = await testRunner.runSuite('End-to-End Integration', 
        integrationResults.map(result => async () => result)
      );
      this.allResults.push(integrationSuite);

      // Run Error Handling and Fallback Tests
      console.log('\n📋 Phase 4: Error Handling and Fallback Testing');
      const errorHandlingResults = await ErrorHandlingFallbackTests.runAllTests();
      const errorHandlingSuite = await testRunner.runSuite('Error Handling & Fallback', 
        errorHandlingResults.map(result => async () => result)
      );
      this.allResults.push(errorHandlingSuite);

      this.endTime = Date.now();

      // Generate and display comprehensive report
      this.generateComprehensiveReport();

    } catch (error) {
      console.error('❌ Test execution failed:', error);
      this.endTime = Date.now();
      this.generateErrorReport(error);
    }
  }

  /**
   * Generate comprehensive test report
   */
  private generateComprehensiveReport(): void {
    const totalDuration = this.endTime - this.startTime;
    const totalTests = this.allResults.reduce((sum, suite) => sum + suite.tests.length, 0);
    const totalPassed = this.allResults.reduce((sum, suite) => sum + suite.passed, 0);
    const totalFailed = this.allResults.reduce((sum, suite) => sum + suite.failed, 0);
    const overallSuccessRate = (totalPassed / totalTests) * 100;

    console.log('\n🎯 COMPREHENSIVE TEST REPORT');
    console.log('=====================================');
    console.log(`📊 Overall Statistics:`);
    console.log(`   Total Test Suites: ${this.allResults.length}`);
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${totalPassed}`);
    console.log(`   Failed: ${totalFailed}`);
    console.log(`   Success Rate: ${overallSuccessRate.toFixed(1)}%`);
    console.log(`   Total Duration: ${(totalDuration / 1000).toFixed(2)}s`);

    // Quality Gates Assessment
    console.log('\n🚪 Quality Gates Assessment:');
    this.assessQualityGates(overallSuccessRate, totalDuration);

    // Detailed Suite Results
    console.log('\n📋 Detailed Suite Results:');
    for (const suite of this.allResults) {
      const suiteSuccessRate = (suite.passed / suite.tests.length) * 100;
      const status = suiteSuccessRate >= 90 ? '✅' : suiteSuccessRate >= 70 ? '⚠️' : '❌';
      
      console.log(`   ${status} ${suite.name}:`);
      console.log(`      Tests: ${suite.tests.length} | Passed: ${suite.passed} | Failed: ${suite.failed}`);
      console.log(`      Success Rate: ${suiteSuccessRate.toFixed(1)}% | Duration: ${suite.totalDuration}ms`);
      
      // Show failed tests
      if (suite.failed > 0) {
        const failedTests = suite.tests.filter(test => !test.passed);
        console.log(`      Failed Tests:`);
        for (const test of failedTests) {
          console.log(`        - ${test.name}: ${test.error}`);
        }
      }
    }

    // Performance Metrics Summary
    console.log('\n⚡ Performance Metrics Summary:');
    this.generatePerformanceMetricsSummary();

    // Translation Quality Summary
    console.log('\n🌍 Translation Quality Summary:');
    this.generateTranslationQualitySummary();

    // Recommendations
    console.log('\n💡 Recommendations:');
    this.generateRecommendations(overallSuccessRate);

    console.log('\n🎉 Test execution completed!');
  }

  /**
   * Assess quality gates
   */
  private assessQualityGates(successRate: number, duration: number): void {
    const gates = [
      { name: 'Overall Success Rate', threshold: 90, value: successRate, unit: '%' },
      { name: 'Test Execution Time', threshold: 60000, value: duration, unit: 'ms', inverse: true },
      { name: 'Translation Accuracy', threshold: 85, value: this.getTranslationAccuracyRate(), unit: '%' },
      { name: 'Performance Compliance', threshold: 90, value: this.getPerformanceComplianceRate(), unit: '%' },
      { name: 'Error Handling Coverage', threshold: 80, value: this.getErrorHandlingCoverageRate(), unit: '%' }
    ];

    for (const gate of gates) {
      const passed = gate.inverse ? gate.value <= gate.threshold : gate.value >= gate.threshold;
      const status = passed ? '✅ PASS' : '❌ FAIL';
      const displayValue = gate.unit === 'ms' ? `${(gate.value / 1000).toFixed(2)}s` : `${gate.value.toFixed(1)}${gate.unit}`;
      const thresholdValue = gate.unit === 'ms' ? `${(gate.threshold / 1000).toFixed(2)}s` : `${gate.threshold}${gate.unit}`;
      
      console.log(`   ${status} ${gate.name}: ${displayValue} (threshold: ${gate.inverse ? '≤' : '≥'} ${thresholdValue})`);
    }
  }

  /**
   * Generate performance metrics summary
   */
  private generatePerformanceMetricsSummary(): void {
    const performanceSuite = this.allResults.find(suite => suite.name === 'Performance Validation');
    if (!performanceSuite) {
      console.log('   No performance data available');
      return;
    }

    const responseTimeTest = performanceSuite.tests.find(test => test.name.includes('Response Time'));
    const concurrentLoadTest = performanceSuite.tests.find(test => test.name.includes('Concurrent'));
    const cacheHitTest = performanceSuite.tests.find(test => test.name.includes('Cache'));

    if (responseTimeTest?.metadata) {
      console.log(`   Average Response Time: ${responseTimeTest.metadata.averageResponseTime}ms`);
      console.log(`   95th Percentile: ${responseTimeTest.metadata.percentile95}ms`);
      console.log(`   Within Threshold: ${responseTimeTest.metadata.withinThresholdPercentage.toFixed(1)}%`);
    }

    if (concurrentLoadTest?.metadata) {
      console.log(`   Concurrent Load Success Rate: ${concurrentLoadTest.metadata.successRate.toFixed(1)}%`);
      console.log(`   Concurrent Requests Handled: ${concurrentLoadTest.metadata.successfulRequests}/${concurrentLoadTest.metadata.concurrentRequests}`);
    }

    if (cacheHitTest?.metadata) {
      console.log(`   Cache Hit Rate: ${cacheHitTest.metadata.cacheHitRate}%`);
    }
  }

  /**
   * Generate translation quality summary
   */
  private generateTranslationQualitySummary(): void {
    const accuracySuite = this.allResults.find(suite => suite.name === 'Translation Accuracy');
    if (!accuracySuite) {
      console.log('   No translation quality data available');
      return;
    }

    const basicTranslationTest = accuracySuite.tests.find(test => test.name.includes('Basic Translation'));
    const businessProfileTest = accuracySuite.tests.find(test => test.name.includes('Business Profile'));
    const culturalAdaptationTest = accuracySuite.tests.find(test => test.name.includes('Cultural Adaptation'));

    if (basicTranslationTest?.metadata) {
      console.log(`   Basic Translation Success: ${basicTranslationTest.metadata.successfulTranslations}/${basicTranslationTest.metadata.expectedTranslations} languages`);
    }

    if (businessProfileTest?.metadata) {
      console.log(`   Business Profile Translation Success: ${businessProfileTest.metadata.successfulTranslations}/3 languages`);
    }

    if (culturalAdaptationTest?.metadata) {
      console.log(`   Cultural Adaptation Success: ${culturalAdaptationTest.metadata.successfulAdaptations}/3 languages`);
    }
  }

  /**
   * Generate recommendations based on test results
   */
  private generateRecommendations(successRate: number): void {
    const recommendations: string[] = [];

    if (successRate < 90) {
      recommendations.push('Overall success rate is below 90%. Review failed tests and improve error handling.');
    }

    const performanceSuite = this.allResults.find(suite => suite.name === 'Performance Validation');
    if (performanceSuite && performanceSuite.passed < performanceSuite.tests.length * 0.9) {
      recommendations.push('Performance tests show issues. Consider optimizing translation service calls and caching strategies.');
    }

    const accuracySuite = this.allResults.find(suite => suite.name === 'Translation Accuracy');
    if (accuracySuite && accuracySuite.passed < accuracySuite.tests.length * 0.85) {
      recommendations.push('Translation accuracy is below threshold. Review translation confidence thresholds and fallback mechanisms.');
    }

    const errorSuite = this.allResults.find(suite => suite.name === 'Error Handling & Fallback');
    if (errorSuite && errorSuite.passed < errorSuite.tests.length * 0.8) {
      recommendations.push('Error handling coverage needs improvement. Enhance graceful degradation and fallback mechanisms.');
    }

    if (recommendations.length === 0) {
      recommendations.push('All quality gates passed! The translation system is ready for production deployment.');
      recommendations.push('Consider monitoring these metrics in production and setting up automated testing in CI/CD pipeline.');
    }

    for (let i = 0; i < recommendations.length; i++) {
      console.log(`   ${i + 1}. ${recommendations[i]}`);
    }
  }

  /**
   * Get translation accuracy rate
   */
  private getTranslationAccuracyRate(): number {
    const accuracySuite = this.allResults.find(suite => suite.name === 'Translation Accuracy');
    if (!accuracySuite || accuracySuite.tests.length === 0) return 0;
    return (accuracySuite.passed / accuracySuite.tests.length) * 100;
  }

  /**
   * Get performance compliance rate
   */
  private getPerformanceComplianceRate(): number {
    const performanceSuite = this.allResults.find(suite => suite.name === 'Performance Validation');
    if (!performanceSuite || performanceSuite.tests.length === 0) return 0;
    return (performanceSuite.passed / performanceSuite.tests.length) * 100;
  }

  /**
   * Get error handling coverage rate
   */
  private getErrorHandlingCoverageRate(): number {
    const errorSuite = this.allResults.find(suite => suite.name === 'Error Handling & Fallback');
    if (!errorSuite || errorSuite.tests.length === 0) return 0;
    return (errorSuite.passed / errorSuite.tests.length) * 100;
  }

  /**
   * Generate error report
   */
  private generateErrorReport(error: any): void {
    console.log('\n❌ TEST EXECUTION ERROR REPORT');
    console.log('=====================================');
    console.log(`Error: ${error instanceof Error ? error.message : String(error)}`);
    console.log(`Duration: ${(this.endTime - this.startTime) / 1000}s`);
    console.log(`Completed Suites: ${this.allResults.length}`);
    
    if (this.allResults.length > 0) {
      console.log('\nPartial Results:');
      for (const suite of this.allResults) {
        console.log(`  ${suite.name}: ${suite.passed}/${suite.tests.length} passed`);
      }
    }
  }
}

/**
 * Main execution function
 */
export async function runAllTests(): Promise<void> {
  const runner = new AfribotTestRunner();
  await runner.runAllTests();
}

// Execute tests if this file is run directly
if (require.main === module) {
  runAllTests().catch(console.error);
}
