/**
 * Error Handling and Fallback Testing Suite
 * Comprehensive tests for graceful degradation and error recovery mechanisms
 */

import { TestUtils, TestResult, TEST_CONFIG } from './setup';
import { translationService } from '../services/translationService';
import { multilingualApiMiddleware } from '../services/multilingualApiMiddleware';
import { businessApi, productsApi } from '../services/api';
import type { SupportedLanguage } from '../types/translation';

/**
 * Error Handling and Fallback Test Suite
 */
export class ErrorHandlingFallbackTests {

  /**
   * Test graceful degradation when translation services are unavailable
   */
  static async testTranslationServiceUnavailableGracefulDegradation(): Promise<TestResult> {
    const testName = 'Translation Service Unavailable - Graceful Degradation';
    console.log(`🚨 Testing: ${testName}`);

    try {
      const { result, duration } = await TestUtils.measureExecutionTime(async () => {
        const results: any[] = [];
        
        // Test business API fallback
        try {
          const mockBusiness = TestUtils.createMockBusiness();
          const context = TestUtils.createTestContext('+254712345678', 'sw');
          
          // Simulate translation service failure by using invalid language
          const businessResponse = await businessApi.getByIdMultilingual(mockBusiness.id, context);
          
          results.push({
            api: 'business',
            hasData: !!businessResponse.data,
            fallbackToOriginal: businessResponse.requestedLanguage === 'sw' && !businessResponse.translation,
            gracefulDegradation: !!businessResponse.data // Should still return original data
          });
          
        } catch (error) {
          results.push({
            api: 'business',
            error: error instanceof Error ? error.message : String(error),
            gracefulDegradation: false
          });
        }
        
        // Test products API fallback
        try {
          const mockBusiness = TestUtils.createMockBusiness();
          const context = TestUtils.createTestContext('+254712345678', 'sw');
          
          const productsResponse = await productsApi.getByBusinessIdMultilingual(mockBusiness.id, context);
          
          results.push({
            api: 'products',
            hasData: Array.isArray(productsResponse.data),
            fallbackToOriginal: productsResponse.requestedLanguage === 'sw' && !productsResponse.translation,
            gracefulDegradation: Array.isArray(productsResponse.data)
          });
          
        } catch (error) {
          results.push({
            api: 'products',
            error: error instanceof Error ? error.message : String(error),
            gracefulDegradation: false
          });
        }
        
        return results;
      });

      const gracefulDegradations = result.filter(r => r.gracefulDegradation).length;
      const totalTests = result.length;
      
      return {
        name: testName,
        passed: gracefulDegradations === totalTests,
        duration,
        metadata: {
          gracefulDegradations,
          totalTests,
          degradationSuccessRate: (gracefulDegradations / totalTests) * 100,
          results: result
        }
      };

    } catch (error) {
      return {
        name: testName,
        passed: false,
        duration: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test fallback mechanisms for low-confidence translations
   */
  static async testLowConfidenceTranslationFallbacks(): Promise<TestResult> {
    const testName = 'Low Confidence Translation Fallbacks';
    console.log(`📉 Testing: ${testName}`);

    try {
      const { result, duration } = await TestUtils.measureExecutionTime(async () => {
        const results: any[] = [];
        
        // Test messages that might produce low confidence translations
        const challengingMessages = [
          'Asante sana kwa huduma nzuri!', // Mixed language
          'Call me @ 0712345678 ASAP!!!', // Mixed content with special chars
          'Hujambo? Habari za asubuhi?', // Informal Swahili
          '$$$ SPECIAL OFFER TODAY ONLY $$$', // Marketing text
          'WiFi password: Keny@2024!', // Technical content
        ];
        
        for (const message of challengingMessages) {
          try {
            const translation = await translationService.translateText(message, 'sw', 'en');
            
            const isLowConfidence = translation.confidence < TEST_CONFIG.confidenceThreshold;
            
            results.push({
              message: message.substring(0, 30) + '...',
              confidence: translation.confidence,
              isLowConfidence,
              hasTranslation: !!translation.translatedText,
              fallbackHandled: isLowConfidence ? !!translation.translatedText : true,
              originalPreserved: isLowConfidence && translation.translatedText === message
            });
            
          } catch (error) {
            results.push({
              message: message.substring(0, 30) + '...',
              error: error instanceof Error ? error.message : String(error),
              fallbackHandled: true // Error handling is a form of fallback
            });
          }
        }
        
        return results;
      });

      const handledFallbacks = result.filter(r => r.fallbackHandled).length;
      const totalTests = result.length;
      
      return {
        name: testName,
        passed: handledFallbacks === totalTests,
        duration,
        metadata: {
          handledFallbacks,
          totalTests,
          fallbackSuccessRate: (handledFallbacks / totalTests) * 100,
          lowConfidenceCount: result.filter(r => r.isLowConfidence).length,
          results: result
        }
      };

    } catch (error) {
      return {
        name: testName,
        passed: false,
        duration: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test error recovery and retry logic for failed translation requests
   */
  static async testErrorRecoveryAndRetryLogic(): Promise<TestResult> {
    const testName = 'Error Recovery and Retry Logic';
    console.log(`🔄 Testing: ${testName}`);

    try {
      const { result, duration } = await TestUtils.measureExecutionTime(async () => {
        const results: any[] = [];
        
        // Test retry mechanism with potentially failing requests
        const testCases = [
          { message: 'Hello world', language: 'sw' as SupportedLanguage },
          { message: 'Thank you', language: 'ha' as SupportedLanguage },
          { message: 'Good morning', language: 'yo' as SupportedLanguage },
          { message: 'How are you?', language: 'fr' as SupportedLanguage }
        ];
        
        for (const testCase of testCases) {
          try {
            // Use retry utility to test retry logic
            const translation = await TestUtils.retry(async () => {
              return await translationService.translateText(
                testCase.message,
                'en',
                testCase.language
              );
            }, 2, 500); // 2 retries, 500ms base delay
            
            results.push({
              message: testCase.message,
              language: testCase.language,
              success: true,
              confidence: translation.confidence,
              retrySuccessful: true
            });
            
          } catch (error) {
            results.push({
              message: testCase.message,
              language: testCase.language,
              success: false,
              error: error instanceof Error ? error.message : String(error),
              retrySuccessful: false
            });
          }
        }
        
        return results;
      });

      const successfulRetries = result.filter(r => r.success).length;
      const totalTests = result.length;
      
      return {
        name: testName,
        passed: successfulRetries >= totalTests * 0.8, // 80% success rate acceptable
        duration,
        metadata: {
          successfulRetries,
          totalTests,
          retrySuccessRate: (successfulRetries / totalTests) * 100,
          results: result
        }
      };

    } catch (error) {
      return {
        name: testName,
        passed: false,
        duration: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test user experience when translation metadata is missing
   */
  static async testMissingTranslationMetadataUserExperience(): Promise<TestResult> {
    const testName = 'Missing Translation Metadata - User Experience';
    console.log(`📋 Testing: ${testName}`);

    try {
      const { result, duration } = await TestUtils.measureExecutionTime(async () => {
        const results: any[] = [];
        
        // Test API responses when translation metadata might be missing
        const mockBusiness = TestUtils.createMockBusiness();
        const context = TestUtils.createTestContext('+254712345678', 'en'); // English - no translation needed
        
        try {
          const businessResponse = await businessApi.getByIdMultilingual(mockBusiness.id, context);
          
          results.push({
            api: 'business',
            hasData: !!businessResponse.data,
            hasTranslationMetadata: !!businessResponse.translation,
            hasAvailableLanguages: Array.isArray(businessResponse.availableLanguages),
            requestedLanguageMatches: businessResponse.requestedLanguage === 'en',
            userExperienceIntact: !!businessResponse.data && Array.isArray(businessResponse.availableLanguages)
          });
          
        } catch (error) {
          results.push({
            api: 'business',
            error: error instanceof Error ? error.message : String(error),
            userExperienceIntact: false
          });
        }
        
        // Test with invalid language preference
        try {
          const invalidContext = TestUtils.createTestContext('+999999999999', 'invalid' as any);
          const businessResponse = await businessApi.getByIdMultilingual(mockBusiness.id, invalidContext);
          
          results.push({
            api: 'business_invalid_lang',
            hasData: !!businessResponse.data,
            fallbackToDefault: businessResponse.requestedLanguage === 'en',
            userExperienceIntact: !!businessResponse.data
          });
          
        } catch (error) {
          results.push({
            api: 'business_invalid_lang',
            error: error instanceof Error ? error.message : String(error),
            userExperienceIntact: false
          });
        }
        
        return results;
      });

      const intactExperiences = result.filter(r => r.userExperienceIntact).length;
      const totalTests = result.length;
      
      return {
        name: testName,
        passed: intactExperiences === totalTests,
        duration,
        metadata: {
          intactExperiences,
          totalTests,
          userExperienceSuccessRate: (intactExperiences / totalTests) * 100,
          results: result
        }
      };

    } catch (error) {
      return {
        name: testName,
        passed: false,
        duration: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test timeout handling and service resilience
   */
  static async testTimeoutHandlingAndServiceResilience(): Promise<TestResult> {
    const testName = 'Timeout Handling and Service Resilience';
    console.log(`⏰ Testing: ${testName}`);

    try {
      const { result, duration } = await TestUtils.measureExecutionTime(async () => {
        const results: any[] = [];
        
        // Test timeout scenarios
        const timeoutTests = [
          { message: 'Short message', expectedTimeout: false },
          { message: 'This is a longer message that might take more time to translate and could potentially timeout in some scenarios', expectedTimeout: false },
          { message: 'Very long message with lots of content that includes technical terms, special characters @#$%^&*(), numbers 123456789, and mixed content that might challenge the translation service and potentially cause timeouts or delays in processing', expectedTimeout: false }
        ];
        
        for (const test of timeoutTests) {
          try {
            const startTime = Date.now();
            
            // Set a reasonable timeout for translation requests
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('Translation timeout')), TEST_CONFIG.translationTimeout);
            });
            
            const translationPromise = translationService.translateText(test.message, 'en', 'sw');
            
            const translation = await Promise.race([translationPromise, timeoutPromise]);
            const requestDuration = Date.now() - startTime;
            
            results.push({
              message: test.message.substring(0, 50) + '...',
              success: true,
              duration: requestDuration,
              withinTimeout: requestDuration <= TEST_CONFIG.translationTimeout,
              confidence: (translation as any).confidence,
              timeoutHandled: true
            });
            
          } catch (error) {
            const isTimeoutError = error instanceof Error && error.message.includes('timeout');
            
            results.push({
              message: test.message.substring(0, 50) + '...',
              success: false,
              error: error instanceof Error ? error.message : String(error),
              isTimeoutError,
              timeoutHandled: isTimeoutError // Timeout errors are handled gracefully
            });
          }
        }
        
        return results;
      });

      const handledTimeouts = result.filter(r => r.timeoutHandled).length;
      const totalTests = result.length;
      
      return {
        name: testName,
        passed: handledTimeouts === totalTests,
        duration,
        metadata: {
          handledTimeouts,
          totalTests,
          timeoutHandlingSuccessRate: (handledTimeouts / totalTests) * 100,
          averageRequestDuration: Math.round(
            result.filter(r => r.duration).reduce((sum, r) => sum + r.duration, 0) / 
            result.filter(r => r.duration).length
          ),
          results: result
        }
      };

    } catch (error) {
      return {
        name: testName,
        passed: false,
        duration: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test language preference fallback when user data is unavailable
   */
  static async testLanguagePreferenceFallback(): Promise<TestResult> {
    const testName = 'Language Preference Fallback';
    console.log(`🌐 Testing: ${testName}`);

    try {
      const { result, duration } = await TestUtils.measureExecutionTime(async () => {
        const results: any[] = [];
        
        // Test fallback scenarios
        const fallbackTests = [
          { phone: '+999999999999', description: 'Unknown country code' },
          { phone: '', description: 'Empty phone number' },
          { phone: 'invalid', description: 'Invalid phone format' },
          { phone: '+1234567890', description: 'Non-African number' }
        ];
        
        for (const test of fallbackTests) {
          try {
            const context = TestUtils.createTestContext(test.phone);
            const detectedPreference = await multilingualApiMiddleware.detectLanguagePreference(context);
            
            results.push({
              phone: test.phone,
              description: test.description,
              detectedLanguage: detectedPreference.language,
              confidence: detectedPreference.confidence,
              source: detectedPreference.source,
              fallbackToDefault: detectedPreference.language === 'en' && detectedPreference.source === 'default',
              gracefulFallback: !!detectedPreference.language
            });
            
          } catch (error) {
            results.push({
              phone: test.phone,
              description: test.description,
              error: error instanceof Error ? error.message : String(error),
              gracefulFallback: false
            });
          }
        }
        
        return results;
      });

      const gracefulFallbacks = result.filter(r => r.gracefulFallback).length;
      const totalTests = result.length;
      
      return {
        name: testName,
        passed: gracefulFallbacks === totalTests,
        duration,
        metadata: {
          gracefulFallbacks,
          totalTests,
          fallbackSuccessRate: (gracefulFallbacks / totalTests) * 100,
          defaultFallbacks: result.filter(r => r.fallbackToDefault).length,
          results: result
        }
      };

    } catch (error) {
      return {
        name: testName,
        passed: false,
        duration: 0,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Run all error handling and fallback tests
   */
  static async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Running Error Handling and Fallback Test Suite');
    
    const tests = [
      this.testTranslationServiceUnavailableGracefulDegradation,
      this.testLowConfidenceTranslationFallbacks,
      this.testErrorRecoveryAndRetryLogic,
      this.testMissingTranslationMetadataUserExperience,
      this.testTimeoutHandlingAndServiceResilience,
      this.testLanguagePreferenceFallback
    ];

    const results: TestResult[] = [];
    
    for (const test of tests) {
      try {
        const result = await test();
        results.push(result);
      } catch (error) {
        results.push({
          name: 'Unknown Error Handling Test',
          passed: false,
          duration: 0,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return results;
  }
}
