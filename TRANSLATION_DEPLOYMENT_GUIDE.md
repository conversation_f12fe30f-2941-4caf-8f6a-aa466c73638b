# AfriBot Translation Services - Production Deployment Guide

## 🚀 **Production Deployment Guide**

**Complete guide for deploying AfriBot's multilingual translation system to production**

**Version**: 1.0.0  
**Last Updated**: December 10, 2024  
**Status**: Production Ready ✅

---

## **📋 Table of Contents**

1. [Pre-Deployment Checklist](#pre-deployment-checklist)
2. [Environment Configuration](#environment-configuration)
3. [Database Setup](#database-setup)
4. [Translation API Configuration](#translation-api-configuration)
5. [Monitoring & Alerting](#monitoring--alerting)
6. [Performance Optimization](#performance-optimization)
7. [Security Configuration](#security-configuration)
8. [Deployment Process](#deployment-process)
9. [Post-Deployment Validation](#post-deployment-validation)
10. [Rollback Procedures](#rollback-procedures)

---

## **✅ Pre-Deployment Checklist**

### **Code Quality Validation**

**Required Commands (ALL must pass):**
```bash
# TypeScript compilation check
npx tsc --noEmit
# Expected: Exit code 0, zero compilation errors

# ESLint validation
npm run lint
# Expected: Exit code 0, zero errors/warnings

# Production build test
npm run build
# Expected: Successful build, no breaking changes

# Test suite execution
npm run test
# Expected: Core tests passing (15/16 minimum)
```

### **System Requirements**

**Infrastructure:**
- [ ] Node.js 18+ environment
- [ ] PostgreSQL 14+ database
- [ ] Redis cache (optional but recommended)
- [ ] Load balancer configuration
- [ ] SSL certificates configured
- [ ] CDN setup for static assets

**External Services:**
- [ ] Google Cloud Translation API access
- [ ] Azure Translator resource (fallback)
- [ ] AWS Translate service (fallback)
- [ ] Supabase project configured
- [ ] Monitoring service (e.g., DataDog, New Relic)

**Security:**
- [ ] API keys secured in environment variables
- [ ] Database connection encrypted
- [ ] CORS configuration updated
- [ ] Rate limiting configured
- [ ] Authentication middleware active

---

## **⚙️ Environment Configuration**

### **Production Environment Variables**

Create `.env.production` file:

```bash
# Application Configuration
NODE_ENV=production
VITE_APP_URL=https://app.afrobot.ai
VITE_API_URL=https://api.afrobot.ai

# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Google Cloud Translation API (Primary Provider)
VITE_GOOGLE_TRANSLATE_API_KEY=your_production_google_key
VITE_GOOGLE_CLOUD_PROJECT_ID=your_project_id
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# Azure Translator (Fallback Provider)
VITE_AZURE_TRANSLATOR_KEY=your_azure_translator_key
VITE_AZURE_TRANSLATOR_REGION=your_azure_region
VITE_AZURE_TRANSLATOR_ENDPOINT=https://api.cognitive.microsofttranslator.com

# AWS Translate (Fallback Provider)
VITE_AWS_ACCESS_KEY_ID=your_aws_access_key
VITE_AWS_SECRET_ACCESS_KEY=your_aws_secret_key
VITE_AWS_REGION=us-east-1

# Translation Service Configuration
VITE_TRANSLATION_CACHE_TTL=1800
VITE_TRANSLATION_MAX_RETRIES=3
VITE_TRANSLATION_TIMEOUT=30000
VITE_TRANSLATION_BATCH_SIZE=50
VITE_TRANSLATION_RATE_LIMIT=1000

# Cost Management
VITE_TRANSLATION_DAILY_BUDGET=500
VITE_TRANSLATION_MONTHLY_BUDGET=10000
VITE_COST_ALERT_THRESHOLD=0.8
VITE_COST_ALERT_EMAIL=<EMAIL>

# Performance Configuration
VITE_CACHE_REDIS_URL=redis://your-redis-instance:6379
VITE_CACHE_DEFAULT_TTL=3600
VITE_MAX_CONCURRENT_TRANSLATIONS=20

# Monitoring & Logging
VITE_SENTRY_DSN=your_sentry_dsn
VITE_ANALYTICS_API_KEY=your_analytics_key
VITE_LOG_LEVEL=info

# WhatsApp Business API
VITE_WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id
VITE_WHATSAPP_ACCESS_TOKEN=your_whatsapp_token
VITE_WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_token

# Security
VITE_JWT_SECRET=your_jwt_secret_key
VITE_ENCRYPTION_KEY=your_encryption_key
VITE_CORS_ORIGINS=https://app.afrobot.ai,https://admin.afrobot.ai
```

### **Environment Validation Script**

Create `scripts/validate-translation-env.js`:

```javascript
const requiredEnvVars = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY',
  'VITE_GOOGLE_TRANSLATE_API_KEY',
  'VITE_GOOGLE_CLOUD_PROJECT_ID'
];

const optionalEnvVars = [
  'VITE_AZURE_TRANSLATOR_KEY',
  'VITE_AWS_ACCESS_KEY_ID'
];

function validateEnvironment() {
  const missing = requiredEnvVars.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:');
    missing.forEach(key => console.error(`  - ${key}`));
    process.exit(1);
  }
  
  console.log('✅ All required environment variables are set');
  
  const missingOptional = optionalEnvVars.filter(key => !process.env[key]);
  if (missingOptional.length > 0) {
    console.warn('⚠️ Missing optional environment variables (fallback providers):');
    missingOptional.forEach(key => console.warn(`  - ${key}`));
  }
}

validateEnvironment();
```

---

## **🗄️ Database Setup**

### **Translation System Migration**

Create `supabase/migrations/20241210_translation_system.sql`:

```sql
-- Translation system database schema
-- Run this migration before deployment

-- User language preferences table
CREATE TABLE IF NOT EXISTS user_language_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone_number TEXT UNIQUE NOT NULL,
  preferred_language TEXT NOT NULL CHECK (preferred_language IN ('en', 'sw', 'ha', 'yo', 'am', 'fr', 'ar', 'pt')),
  detected_language TEXT CHECK (detected_language IN ('en', 'sw', 'ha', 'yo', 'am', 'fr', 'ar', 'pt')),
  confidence DECIMAL(3,2) CHECK (confidence >= 0 AND confidence <= 1),
  manually_set BOOLEAN DEFAULT false,
  last_interaction TIMESTAMPTZ DEFAULT now(),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Translation cache for performance optimization
CREATE TABLE IF NOT EXISTS translation_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  source_text_hash TEXT NOT NULL,
  source_language TEXT NOT NULL,
  target_language TEXT NOT NULL,
  translated_text TEXT NOT NULL,
  confidence DECIMAL(3,2),
  provider TEXT NOT NULL CHECK (provider IN ('google', 'azure', 'aws')),
  context_hash TEXT,
  character_count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT now(),
  expires_at TIMESTAMPTZ NOT NULL,
  UNIQUE(source_text_hash, source_language, target_language, context_hash)
);

-- Content translations for business content
CREATE TABLE IF NOT EXISTS content_translations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
  content_type TEXT NOT NULL CHECK (content_type IN ('business_profile', 'product', 'faq', 'template')),
  content_id TEXT NOT NULL,
  original_language TEXT NOT NULL DEFAULT 'en',
  target_language TEXT NOT NULL,
  original_text JSONB NOT NULL,
  translated_text JSONB NOT NULL,
  confidence DECIMAL(3,2),
  provider TEXT NOT NULL,
  cost DECIMAL(10,4),
  character_count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(business_id, content_type, content_id, target_language)
);

-- Translation analytics and usage tracking
CREATE TABLE IF NOT EXISTS translation_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
  phone_number TEXT,
  source_language TEXT NOT NULL,
  target_language TEXT NOT NULL,
  message_type TEXT CHECK (message_type IN ('incoming', 'outgoing', 'content', 'bulk')),
  confidence DECIMAL(3,2),
  processing_time INTEGER, -- milliseconds
  provider TEXT NOT NULL,
  cost DECIMAL(10,4),
  character_count INTEGER NOT NULL DEFAULT 0,
  cached BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Translation cost tracking
CREATE TABLE IF NOT EXISTS translation_costs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
  provider TEXT NOT NULL,
  language_pair TEXT NOT NULL, -- 'en-sw', 'en-ha', etc.
  character_count INTEGER NOT NULL,
  cost DECIMAL(10,4) NOT NULL,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Bulk translation jobs tracking
CREATE TABLE IF NOT EXISTS bulk_translation_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
  job_type TEXT NOT NULL CHECK (job_type IN ('content_translation', 'bulk_update')),
  status TEXT NOT NULL DEFAULT 'queued' CHECK (status IN ('queued', 'in_progress', 'completed', 'failed', 'cancelled')),
  total_items INTEGER NOT NULL DEFAULT 0,
  completed_items INTEGER NOT NULL DEFAULT 0,
  failed_items INTEGER NOT NULL DEFAULT 0,
  estimated_cost DECIMAL(10,4),
  actual_cost DECIMAL(10,4),
  target_languages TEXT[] NOT NULL,
  content_types TEXT[] NOT NULL,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_language_preferences_phone ON user_language_preferences(phone_number);
CREATE INDEX IF NOT EXISTS idx_translation_cache_lookup ON translation_cache(source_text_hash, source_language, target_language);
CREATE INDEX IF NOT EXISTS idx_translation_cache_expires ON translation_cache(expires_at);
CREATE INDEX IF NOT EXISTS idx_content_translations_business ON content_translations(business_id, content_type);
CREATE INDEX IF NOT EXISTS idx_translation_analytics_business_date ON translation_analytics(business_id, created_at);
CREATE INDEX IF NOT EXISTS idx_translation_costs_business_date ON translation_costs(business_id, date);
CREATE INDEX IF NOT EXISTS idx_bulk_jobs_business_status ON bulk_translation_jobs(business_id, status);

-- Create functions for automatic cleanup
CREATE OR REPLACE FUNCTION cleanup_expired_cache()
RETURNS void AS $$
BEGIN
  DELETE FROM translation_cache WHERE expires_at < now();
END;
$$ LANGUAGE plpgsql;

-- Schedule cache cleanup (run daily)
SELECT cron.schedule('cleanup-translation-cache', '0 2 * * *', 'SELECT cleanup_expired_cache();');
```

### **Row Level Security Setup**

Create `supabase/migrations/20241210_translation_rls.sql`:

```sql
-- Enable Row Level Security on all translation tables
ALTER TABLE user_language_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE translation_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_translations ENABLE ROW LEVEL SECURITY;
ALTER TABLE translation_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE translation_costs ENABLE ROW LEVEL SECURITY;
ALTER TABLE bulk_translation_jobs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_language_preferences
CREATE POLICY "Users can manage their language preferences" ON user_language_preferences
  FOR ALL USING (
    auth.uid()::text = phone_number OR 
    auth.role() = 'service_role' OR
    EXISTS (
      SELECT 1 FROM businesses 
      WHERE user_id = auth.uid() AND phone_number = user_language_preferences.phone_number
    )
  );

-- RLS Policies for translation_cache (service role only for performance)
CREATE POLICY "Service role can manage translation cache" ON translation_cache
  FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for content_translations
CREATE POLICY "Businesses can manage their content translations" ON content_translations
  FOR ALL USING (
    business_id IN (
      SELECT id FROM businesses WHERE user_id = auth.uid()
    ) OR auth.role() = 'service_role'
  );

-- RLS Policies for translation_analytics
CREATE POLICY "Businesses can view their translation analytics" ON translation_analytics
  FOR SELECT USING (
    business_id IN (
      SELECT id FROM businesses WHERE user_id = auth.uid()
    ) OR auth.role() = 'service_role'
  );

CREATE POLICY "Service role can insert analytics" ON translation_analytics
  FOR INSERT WITH CHECK (auth.role() = 'service_role');

-- RLS Policies for translation_costs
CREATE POLICY "Businesses can view their translation costs" ON translation_costs
  FOR SELECT USING (
    business_id IN (
      SELECT id FROM businesses WHERE user_id = auth.uid()
    ) OR auth.role() = 'service_role'
  );

CREATE POLICY "Service role can manage costs" ON translation_costs
  FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for bulk_translation_jobs
CREATE POLICY "Businesses can manage their bulk jobs" ON bulk_translation_jobs
  FOR ALL USING (
    business_id IN (
      SELECT id FROM businesses WHERE user_id = auth.uid()
    ) OR auth.role() = 'service_role'
  );
```

### **Database Migration Commands**

```bash
# Apply migrations to production
supabase db push --db-url "postgresql://user:pass@host:port/db"

# Verify migration status
supabase migration list --db-url "postgresql://user:pass@host:port/db"

# Create database backup before migration
pg_dump -h host -U user -d database > backup_pre_translation_$(date +%Y%m%d).sql
```

---

## **🔑 Translation API Configuration**

### **Google Cloud Translation API Setup**

**1. Create Service Account:**
```bash
# Create service account
gcloud iam service-accounts create afrobot-translation \
  --display-name="AfriBot Translation Service"

# Grant necessary permissions
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
  --member="serviceAccount:afrobot-translation@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/translate.user"

# Create and download key
gcloud iam service-accounts keys create service-account-key.json \
  --iam-account=afrobot-translation@YOUR_PROJECT_ID.iam.gserviceaccount.com
```

**2. Enable APIs:**
```bash
# Enable Translation API
gcloud services enable translate.googleapis.com

# Enable Cloud Resource Manager API
gcloud services enable cloudresourcemanager.googleapis.com
```

**3. Set Quotas and Limits:**
- Characters per day: 1,000,000
- Characters per minute: 10,000
- Requests per minute: 1,000

### **Azure Translator Setup**

**1. Create Translator Resource:**
```bash
# Create resource group
az group create --name afrobot-translation --location eastus

# Create Translator resource
az cognitiveservices account create \
  --name afrobot-translator \
  --resource-group afrobot-translation \
  --kind TextTranslation \
  --sku S1 \
  --location eastus
```

**2. Get Keys:**
```bash
# Get access keys
az cognitiveservices account keys list \
  --name afrobot-translator \
  --resource-group afrobot-translation
```

### **AWS Translate Setup**

**1. Create IAM User:**
```bash
# Create IAM user
aws iam create-user --user-name afrobot-translate

# Attach policy
aws iam attach-user-policy \
  --user-name afrobot-translate \
  --policy-arn arn:aws:iam::aws:policy/TranslateReadOnly

# Create access keys
aws iam create-access-key --user-name afrobot-translate
```

---

## **📊 Monitoring & Alerting**

### **Health Check Endpoints**

Create `src/api/translation-health.ts`:

```typescript
// Health check for translation services
export async function GET() {
  const healthChecks = {
    translation_service: false,
    database: false,
    cache: false,
    google_translate: false,
    azure_translate: false,
    aws_translate: false
  };

  try {
    // Check database connection
    const { error: dbError } = await supabase
      .from('businesses')
      .select('id')
      .limit(1);
    healthChecks.database = !dbError;

    // Check translation providers
    healthChecks.google_translate = await checkGoogleTranslate();
    healthChecks.azure_translate = await checkAzureTranslate();
    healthChecks.aws_translate = await checkAWSTranslate();

    const allHealthy = Object.values(healthChecks).every(Boolean);

    return Response.json({
      status: allHealthy ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      checks: healthChecks
    }, {
      status: allHealthy ? 200 : 503
    });
  } catch (error) {
    return Response.json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 503 });
  }
}
```

### **Monitoring Metrics**

**Key Metrics to Monitor:**
- Translation API response times
- Translation success/failure rates
- Translation confidence scores
- Cache hit rates
- Cost per translation
- Daily/monthly cost totals
- Database query performance
- Error rates by provider

**Alerting Thresholds:**
```yaml
# Example monitoring configuration
alerts:
  translation_response_time:
    threshold: 2000ms
    severity: warning

  translation_failure_rate:
    threshold: 5%
    severity: critical

  daily_cost_limit:
    threshold: $500
    severity: warning

  monthly_cost_limit:
    threshold: $10000
    severity: critical

  cache_hit_rate:
    threshold: 50%
    severity: warning
```

---

## **⚡ Performance Optimization**

### **Caching Strategy**

**Redis Configuration:**
```bash
# Redis configuration for translation caching
redis-server --maxmemory 2gb --maxmemory-policy allkeys-lru
```

**Cache Implementation:**
```typescript
// Translation cache configuration
const CACHE_CONFIG = {
  translation_ttl: 1800, // 30 minutes
  content_ttl: 3600, // 1 hour
  analytics_ttl: 300, // 5 minutes
  max_cache_size: '1GB'
};
```

### **Database Optimization**

**Connection Pooling:**
```typescript
// Supabase connection pooling
const supabase = createClient(url, key, {
  db: {
    schema: 'public',
  },
  auth: {
    autoRefreshToken: true,
    persistSession: true,
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});
```

**Query Optimization:**
- Use indexes on frequently queried columns
- Implement pagination for large result sets
- Use prepared statements for repeated queries
- Monitor slow query logs

### **Load Balancing**

**Translation Service Load Balancing:**
```nginx
# Nginx configuration for translation services
upstream translation_backend {
    least_conn;
    server translation1.afrobot.ai:3000;
    server translation2.afrobot.ai:3000;
    server translation3.afrobot.ai:3000;
}

server {
    listen 443 ssl;
    server_name api.afrobot.ai;

    location /api/translation/ {
        proxy_pass http://translation_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
```

---

## **🔒 Security Configuration**

### **API Key Management**

**Environment-based Key Rotation:**
```bash
# Key rotation script
#!/bin/bash
# rotate-translation-keys.sh

# Backup current keys
cp .env.production .env.production.backup.$(date +%Y%m%d)

# Update Google Cloud key
gcloud iam service-accounts keys create new-key.json \
  --iam-account=afrobot-translation@PROJECT_ID.iam.gserviceaccount.com

# Update environment variables
sed -i 's/VITE_GOOGLE_TRANSLATE_API_KEY=.*/VITE_GOOGLE_TRANSLATE_API_KEY=new_key/' .env.production

# Restart services
systemctl restart afrobot-translation
```

### **Rate Limiting**

**Translation API Rate Limiting:**
```typescript
// Rate limiting configuration
const rateLimitConfig = {
  windowMs: 60 * 1000, // 1 minute
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many translation requests, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
};
```

### **Input Validation**

**Translation Input Sanitization:**
```typescript
// Input validation for translation requests
const translationSchema = z.object({
  text: z.string().min(1).max(5000),
  sourceLanguage: z.enum(['en', 'sw', 'ha', 'yo', 'am', 'fr', 'ar', 'pt']),
  targetLanguage: z.enum(['en', 'sw', 'ha', 'yo', 'am', 'fr', 'ar', 'pt']),
  businessId: z.string().uuid().optional(),
});
```

---

## **🚀 Deployment Process**

### **Deployment Steps**

**1. Pre-deployment Validation:**
```bash
# Run quality assurance pipeline
npm run qa

# Validate environment configuration
node scripts/validate-translation-env.js

# Test database connectivity
npm run test:db

# Verify translation API access
npm run test:translation-apis
```

**2. Database Migration:**
```bash
# Create backup
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# Apply migrations
supabase db push

# Verify migration success
supabase migration list
```

**3. Application Deployment:**
```bash
# Build production assets
npm run build

# Deploy to production
# (Vercel example)
vercel --prod

# (Custom server example)
pm2 start ecosystem.config.js --env production
```

**4. Service Verification:**
```bash
# Check application health
curl https://api.afrobot.ai/health

# Check translation service health
curl https://api.afrobot.ai/api/translation-health

# Verify database connectivity
curl https://api.afrobot.ai/api/db-health
```

### **Deployment Checklist**

**Pre-deployment:**
- [ ] All environment variables configured
- [ ] Database migrations applied
- [ ] API keys validated and tested
- [ ] Translation providers configured
- [ ] Monitoring and alerting set up
- [ ] Cost budgets and limits configured
- [ ] Load balancer configuration updated
- [ ] SSL certificates renewed
- [ ] Backup procedures verified

**Quality Assurance:**
- [ ] TypeScript compilation: `npx tsc --noEmit`
- [ ] Linting validation: `npm run lint`
- [ ] Production build: `npm run build`
- [ ] Test suite execution: `npm run test`
- [ ] Integration testing completed
- [ ] Performance testing passed
- [ ] Security scanning completed

**Post-deployment:**
- [ ] Translation services health check
- [ ] API endpoint validation
- [ ] Performance monitoring active
- [ ] Cost tracking operational
- [ ] User language preferences working
- [ ] WhatsApp integration functional
- [ ] Error tracking and logging active
- [ ] Backup procedures tested

---

## **✅ Post-Deployment Validation**

### **Functional Testing**

**Translation Service Testing:**
```bash
# Test basic translation
curl -X POST https://api.afrobot.ai/api/translate \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello world", "targetLanguage": "sw"}'

# Expected response:
# {"translatedText": "Hujambo dunia", "confidence": 0.95, "provider": "google"}
```

**Database Connectivity:**
```bash
# Test database queries
curl https://api.afrobot.ai/api/businesses/test-business-id?lang=sw

# Expected: Business data translated to Swahili
```

**WhatsApp Integration:**
```bash
# Send test message to WhatsApp webhook
curl -X POST https://api.afrobot.ai/api/webhook/whatsapp \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"from": "+254712345678", "text": {"body": "Habari"}}]}'
```

### **Performance Validation**

**Load Testing:**
```bash
# Use Apache Bench for load testing
ab -n 1000 -c 10 https://api.afrobot.ai/api/translate

# Monitor response times and error rates
```

**Cache Performance:**
```bash
# Test cache hit rates
redis-cli info stats | grep keyspace_hits
redis-cli info stats | grep keyspace_misses
```

### **Monitoring Validation**

**Check Monitoring Dashboards:**
- Translation API response times
- Error rates by provider
- Cost tracking accuracy
- Cache hit rates
- Database performance metrics

**Verify Alerting:**
- Test cost limit alerts
- Test performance degradation alerts
- Test error rate threshold alerts

---

## **🔄 Rollback Procedures**

### **Emergency Rollback**

**Immediate Actions:**
```bash
# 1. Disable translation features via feature flag
curl -X POST https://api.afrobot.ai/api/admin/feature-flags \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -d '{"translation_enabled": false}'

# 2. Revert to previous deployment
vercel rollback --token $VERCEL_TOKEN

# 3. Restore database if needed
psql $DATABASE_URL < backup_pre_translation_20241210.sql
```

### **Gradual Rollback**

**Step-by-step Rollback:**
1. **Reduce Translation Usage:**
   - Increase cache TTL to reduce API calls
   - Disable specific language pairs
   - Reduce translation rate limits

2. **Monitor System Stability:**
   - Watch error rates and response times
   - Monitor cost impact
   - Check user experience metrics

3. **Complete Rollback if Needed:**
   - Disable all translation features
   - Revert to English-only mode
   - Notify users of temporary limitation

### **Rollback Validation**

**Post-rollback Checks:**
- [ ] Application functionality restored
- [ ] Database integrity verified
- [ ] User authentication working
- [ ] WhatsApp integration functional
- [ ] Payment processing active
- [ ] Monitoring systems operational

---

## **📞 Support & Troubleshooting**

### **Common Deployment Issues**

**Translation API Not Working:**
```bash
# Check API key configuration
echo $VITE_GOOGLE_TRANSLATE_API_KEY | head -c 20

# Test API connectivity
curl "https://translation.googleapis.com/language/translate/v2?key=$VITE_GOOGLE_TRANSLATE_API_KEY" \
  -d "q=hello&target=sw&source=en"
```

**Database Connection Issues:**
```bash
# Test database connectivity
psql $DATABASE_URL -c "SELECT version();"

# Check migration status
supabase migration list
```

**High Translation Costs:**
```bash
# Check cache hit rates
redis-cli info stats

# Review translation analytics
curl https://api.afrobot.ai/api/analytics/translation/costs
```

### **Support Contacts**

**Technical Support:**
- Email: <EMAIL>
- Slack: #afrobot-support
- Phone: +254-XXX-XXXX (24/7 for production issues)

**Documentation:**
- Technical Docs: https://docs.afrobot.ai
- API Reference: https://api.afrobot.ai/docs
- GitHub Issues: https://github.com/afrobot/issues

---

**🎉 AfriBot Translation System is now successfully deployed to production with comprehensive multilingual support for African SMEs!** 🌍🚀

**Next Steps:**
1. Monitor system performance and costs
2. Gather user feedback on translation quality
3. Optimize based on usage patterns
4. Plan for additional language support
5. Scale infrastructure as needed
