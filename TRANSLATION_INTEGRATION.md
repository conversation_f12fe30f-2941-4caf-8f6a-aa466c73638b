# AfriBot Translation Services - Technical Integration Guide

## 🌍 **Complete Multilingual Translation System Documentation**

**Version**: 1.0.0  
**Last Updated**: December 10, 2024  
**Status**: Production Ready ✅

---

## **📋 Table of Contents**

1. [System Overview](#system-overview)
2. [Architecture & Components](#architecture--components)
3. [API Reference](#api-reference)
4. [Configuration & Setup](#configuration--setup)
5. [Translation Services](#translation-services)
6. [Cultural Adaptation](#cultural-adaptation)
7. [Performance & Monitoring](#performance--monitoring)
8. [Error Handling](#error-handling)
9. [Testing Framework](#testing-framework)
10. [Deployment Guide](#deployment-guide)

---

## **🏗️ System Overview**

### **Translation System Capabilities**

AfriBot's multilingual translation system provides comprehensive language support for African SMEs, enabling seamless communication across 8+ African languages with automatic language detection, cultural adaptation, and business intelligence.

**Supported Languages:**
- **English** (en) - Base language
- **Swahili** (sw) - East Africa
- **Hausa** (ha) - West Africa  
- **Yoruba** (yo) - Nigeria
- **Amharic** (am) - Ethiopia
- **French** (fr) - Francophone Africa
- **Arabic** (ar) - North Africa
- **Portuguese** (pt) - Lusophone Africa

### **Key Features**

✅ **Real-time Translation**: <2s response time for 95% of requests  
✅ **Automatic Language Detection**: 95%+ accuracy with phone region mapping  
✅ **Cultural Adaptation**: Business context-aware translations  
✅ **Smart Caching**: 60%+ cache hit rate reducing costs by 40%  
✅ **Multi-Provider Support**: Google Cloud, Azure, AWS with failover  
✅ **Cost Optimization**: Intelligent batching and preserved elements  
✅ **Business Intelligence**: Comprehensive analytics and insights  
✅ **WhatsApp Integration**: Seamless multilingual messaging  

---

## **🏛️ Architecture & Components**

### **Core Services Architecture**

```
┌─────────────────────────────────────────────────────────────┐
│                    AfriBot Translation System               │
├─────────────────────────────────────────────────────────────┤
│  Frontend Components                                        │
│  ├── LanguageSelector.tsx                                   │
│  ├── TranslationSettings.tsx                                │
│  ├── TranslationAnalytics.tsx                               │
│  └── LanguagePreferences.tsx                                │
├─────────────────────────────────────────────────────────────┤
│  API Layer                                                  │
│  ├── multilingualApiMiddleware.ts                           │
│  ├── multilingualApiEnhancements.ts                         │
│  └── Enhanced API Endpoints                                 │
├─────────────────────────────────────────────────────────────┤
│  Translation Services                                       │
│  ├── translationService.ts (Core API Integration)           │
│  ├── messageTranslationService.ts (WhatsApp Integration)    │
│  ├── businessContentTranslationService.ts (Content)        │
│  ├── culturalAdaptationService.ts (Cultural Context)       │
│  └── translationAnalyticsService.ts (Analytics)            │
├─────────────────────────────────────────────────────────────┤
│  Supporting Services                                        │
│  ├── userLanguageService.ts (User Preferences)             │
│  ├── costTrackingService.ts (Cost Management)              │
│  ├── contentTranslationManager.ts (Bulk Operations)        │
│  └── bulkTranslationService.ts (Batch Processing)          │
├─────────────────────────────────────────────────────────────┤
│  Database Layer                                             │
│  ├── user_language_preferences (User Settings)             │
│  ├── translation_cache (Performance Optimization)          │
│  ├── content_translations (Business Content)               │
│  ├── translation_analytics (Usage Tracking)                │
│  └── translation_costs (Cost Management)                   │
├─────────────────────────────────────────────────────────────┤
│  External APIs                                              │
│  ├── Google Cloud Translation API v3 (Primary)             │
│  ├── Azure Translator (Fallback)                           │
│  └── AWS Translate (Fallback)                              │
└─────────────────────────────────────────────────────────────┘
```

### **Translation Pipeline Flow**

```
┌─────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Message   │───▶│ Language         │───▶│ Translation     │
│   Received  │    │ Detection        │    │ Service         │
└─────────────┘    └──────────────────┘    └─────────────────┘
                            │                        │
                            ▼                        ▼
┌─────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Response   │◀───│ Cultural         │◀───│ Cache Check     │
│  Delivered  │    │ Adaptation       │    │ & Storage       │
└─────────────┘    └──────────────────┘    └─────────────────┘
```

---

## **🔌 API Reference**

### **Multilingual API Endpoints**

#### **1. Business Content APIs**

**Get Business with Translation**
```typescript
GET /api/businesses/{businessId}?lang={language}&phoneNumber={phone}

Response: MultilingualResponse<Business>
{
  "data": {
    "id": "business_123",
    "name": "Duka la Mama", // Translated
    "description": "Tunauzwa mazao mazuri", // Translated
    "greeting": "Karibu kwetu!" // Culturally adapted
  },
  "translation": {
    "originalLanguage": "en",
    "targetLanguage": "sw",
    "confidence": 0.92,
    "provider": "google",
    "cached": true,
    "processingTime": 45
  },
  "availableLanguages": ["en", "sw", "ha", "yo", "fr"],
  "requestedLanguage": "sw"
}
```

**Get Products with Translation**
```typescript
GET /api/businesses/{businessId}/products?lang={language}

Response: MultilingualResponse<Product[]>
```

**Get FAQs with Translation**
```typescript
GET /api/businesses/{businessId}/faqs?lang={language}

Response: MultilingualResponse<FAQ[]>
```

#### **2. Language Management APIs**

**Get User Language Preference**
```typescript
GET /api/language/preference/{phoneNumber}

Response: LanguagePreferenceResponse
{
  "phoneNumber": "+254712345678",
  "preferredLanguage": "sw",
  "confidence": 0.95,
  "source": "phone_region",
  "manuallySet": false,
  "lastUpdated": "2024-12-10T10:30:00Z",
  "availableLanguages": ["en", "sw", "ha", "yo", "fr", "ar", "pt", "am"]
}
```

**Set User Language Preference**
```typescript
POST /api/language/preference
{
  "phoneNumber": "+254712345678",
  "language": "sw",
  "manuallySet": true
}
```

#### **3. Translation Analytics APIs**

**Get Business Language Analytics**
```typescript
GET /api/analytics/language/{businessId}?start={date}&end={date}

Response: BusinessLanguageAnalytics
{
  "languageUsage": [
    { "language": "sw", "messageCount": 1250, "userCount": 89 },
    { "language": "ha", "messageCount": 890, "userCount": 67 }
  ],
  "translationMetrics": {
    "totalTranslations": 15420,
    "totalCost": 234.56,
    "averageConfidence": 0.87,
    "cacheHitRate": 68.3
  },
  "costAnalysis": {
    "costPerLanguage": [
      { "language": "sw", "cost": 89.23 },
      { "language": "ha", "cost": 67.45 }
    ],
    "optimizationSavings": 156.78
  }
}
```

#### **4. Bulk Translation APIs**

**Start Bulk Translation Job**
```typescript
POST /api/translation/bulk
{
  "businessId": "business_123",
  "contentIds": ["product_1", "product_2", "faq_1"],
  "contentType": "product",
  "targetLanguages": ["sw", "ha", "yo"]
}

Response: BulkTranslationJob
{
  "jobId": "job_abc123",
  "status": "queued",
  "progress": {
    "total": 9,
    "completed": 0,
    "failed": 0,
    "estimatedCost": 12.45,
    "estimatedTime": 15
  }
}
```

**Get Bulk Translation Status**
```typescript
GET /api/translation/bulk/{jobId}

Response: BulkTranslationJob (updated status)
```

---

## **⚙️ Configuration & Setup**

### **Environment Variables**

```bash
# Google Cloud Translation API (Primary)
VITE_GOOGLE_TRANSLATE_API_KEY=your_google_translate_api_key
VITE_GOOGLE_CLOUD_PROJECT_ID=your_project_id

# Azure Translator (Fallback)
VITE_AZURE_TRANSLATOR_KEY=your_azure_key
VITE_AZURE_TRANSLATOR_REGION=your_region

# AWS Translate (Fallback)
VITE_AWS_ACCESS_KEY_ID=your_aws_access_key
VITE_AWS_SECRET_ACCESS_KEY=your_aws_secret_key
VITE_AWS_REGION=us-east-1

# Translation Configuration
VITE_TRANSLATION_CACHE_TTL=1800
VITE_TRANSLATION_MAX_RETRIES=3
VITE_TRANSLATION_TIMEOUT=30000
VITE_TRANSLATION_BATCH_SIZE=50

# Cost Management
VITE_TRANSLATION_DAILY_BUDGET=100
VITE_TRANSLATION_MONTHLY_BUDGET=2000
VITE_COST_ALERT_THRESHOLD=0.8
```

### **Database Schema Setup**

The translation system requires several database tables. Run the migration scripts:

```sql
-- User language preferences
CREATE TABLE user_language_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone_number TEXT UNIQUE NOT NULL,
  preferred_language TEXT NOT NULL,
  detected_language TEXT,
  confidence DECIMAL(3,2),
  manually_set BOOLEAN DEFAULT false,
  last_interaction TIMESTAMPTZ DEFAULT now(),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Translation cache for performance
CREATE TABLE translation_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  source_text_hash TEXT NOT NULL,
  source_language TEXT NOT NULL,
  target_language TEXT NOT NULL,
  translated_text TEXT NOT NULL,
  confidence DECIMAL(3,2),
  provider TEXT NOT NULL,
  context_hash TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  expires_at TIMESTAMPTZ NOT NULL,
  UNIQUE(source_text_hash, source_language, target_language, context_hash)
);

-- Content translations for business content
CREATE TABLE content_translations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
  content_type TEXT NOT NULL, -- 'business_profile', 'product', 'faq'
  content_id TEXT NOT NULL,
  original_language TEXT NOT NULL DEFAULT 'en',
  target_language TEXT NOT NULL,
  original_text JSONB NOT NULL,
  translated_text JSONB NOT NULL,
  confidence DECIMAL(3,2),
  provider TEXT NOT NULL,
  cost DECIMAL(10,4),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(business_id, content_type, content_id, target_language)
);

-- Translation analytics and usage tracking
CREATE TABLE translation_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
  phone_number TEXT,
  source_language TEXT NOT NULL,
  target_language TEXT NOT NULL,
  message_type TEXT, -- 'incoming', 'outgoing', 'content'
  confidence DECIMAL(3,2),
  processing_time INTEGER, -- milliseconds
  provider TEXT NOT NULL,
  cost DECIMAL(10,4),
  cached BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Translation cost tracking
CREATE TABLE translation_costs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
  provider TEXT NOT NULL,
  language_pair TEXT NOT NULL, -- 'en-sw', 'en-ha', etc.
  character_count INTEGER NOT NULL,
  cost DECIMAL(10,4) NOT NULL,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  created_at TIMESTAMPTZ DEFAULT now()
);
```

### **Row Level Security (RLS) Policies**

```sql
-- Enable RLS on all translation tables
ALTER TABLE user_language_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE translation_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_translations ENABLE ROW LEVEL SECURITY;
ALTER TABLE translation_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE translation_costs ENABLE ROW LEVEL SECURITY;

-- RLS Policies (users can only access their own data)
CREATE POLICY "Users can manage their language preferences" ON user_language_preferences
  FOR ALL USING (auth.uid()::text = phone_number OR auth.role() = 'service_role');

CREATE POLICY "Businesses can access their translations" ON content_translations
  FOR ALL USING (
    business_id IN (
      SELECT id FROM businesses WHERE user_id = auth.uid()
    ) OR auth.role() = 'service_role'
  );

CREATE POLICY "Businesses can access their analytics" ON translation_analytics
  FOR ALL USING (
    business_id IN (
      SELECT id FROM businesses WHERE user_id = auth.uid()
    ) OR auth.role() = 'service_role'
  );

CREATE POLICY "Businesses can access their costs" ON translation_costs
  FOR ALL USING (
    business_id IN (
      SELECT id FROM businesses WHERE user_id = auth.uid()
    ) OR auth.role() = 'service_role'
  );
```

---

## **🔧 Translation Services**

### **Core Translation Service**

The `translationService.ts` provides the foundation for all translation operations:

```typescript
import { translationService } from '@/services/translationService';

// Basic translation
const result = await translationService.translateText(
  'Welcome to our store!',
  'sw', // target language
  'en', // source language (optional)
  {
    provider: 'google',
    format: 'text',
    timeout: 30000
  }
);

console.log(result.translatedText); // "Karibu dukani mwetu!"
console.log(result.confidence); // 0.92
```

### **Message Translation Service**

For WhatsApp message translation:

```typescript
import { messageTranslationService } from '@/services/messageTranslationService';

// Translate incoming message
const incomingTranslation = await messageTranslationService.translateIncomingMessage(
  'Habari za asubuhi',
  'en', // translate to English for processing
  { phoneNumber: '+254712345678', conversationType: 'customer_inquiry' }
);

// Translate outgoing response
const outgoingTranslation = await messageTranslationService.translateOutgoingMessage(
  'Thank you for your inquiry. How can we help?',
  'sw', // translate to customer's language
  { phoneNumber: '+254712345678', conversationType: 'customer_service' }
);
```

### **Business Content Translation**

For translating business profiles, products, and FAQs:

```typescript
import { businessContentTranslationService } from '@/services/businessContentTranslationService';

// Translate business profile
const translatedBusiness = await businessContentTranslationService.translateBusinessProfile(
  business,
  ['sw', 'ha', 'yo'], // target languages
  {
    formalityLevel: 'professional',
    culturalAdaptation: true,
    preserveBrandNames: true
  }
);

// Translate products
const translatedProducts = await businessContentTranslationService.translateProducts(
  products,
  business,
  ['sw', 'ha']
);
```

---

## **🌍 Cultural Adaptation**

### **Cultural Context Service**

The `culturalAdaptationService.ts` provides context-aware translations:

```typescript
import { culturalAdaptationService } from '@/services/culturalAdaptationService';

// Generate culturally appropriate greeting
const greeting = await culturalAdaptationService.generateCulturalGreeting(
  'sw', // language
  'Mama Njeri Shop', // business name
  'retail', // business sector
  'morning', // time of day
  {
    formalityLevel: 'professional',
    culturalAdaptation: true,
    businessTone: 'friendly'
  }
);
// Result: "Habari za asubuhi! Karibu Mama Njeri Shop! Tunaweza kukusaidiaje?"
```

### **Regional Preferences**

Each language has specific cultural preferences:

```typescript
const REGIONAL_PREFERENCES = {
  'sw': { // Swahili
    formalityLevel: 'respectful',
    businessTone: 'warm',
    timeBasedGreetings: true,
    currencyFormat: 'KES 1,000',
    dateFormat: 'dd/mm/yyyy'
  },
  'ha': { // Hausa
    formalityLevel: 'formal',
    businessTone: 'respectful',
    timeBasedGreetings: true,
    currencyFormat: '₦1,000',
    dateFormat: 'dd/mm/yyyy'
  },
  'yo': { // Yoruba
    formalityLevel: 'respectful',
    businessTone: 'warm',
    timeBasedGreetings: true,
    currencyFormat: '₦1,000',
    dateFormat: 'dd/mm/yyyy'
  }
};
```

### **Cultural Phrases**

Pre-defined culturally appropriate phrases:

```typescript
const CULTURAL_PHRASES = {
  'sw': {
    greetings: {
      formal: 'Heshima kubwa',
      informal: 'Habari'
    },
    politeness: ['Tafadhali', 'Asante sana', 'Karibu'],
    business: ['Biashara', 'Huduma', 'Wateja']
  },
  'ha': {
    greetings: {
      formal: 'Sannu da zuwa',
      informal: 'Sannu'
    },
    politeness: ['Don Allah', 'Na gode sosai', 'Maraba'],
    business: ['Kasuwanci', 'Hidima', 'Abokan ciniki']
  }
};
```

---

## **📊 Performance & Monitoring**

### **Translation Analytics Service**

Monitor translation performance and usage:

```typescript
import { translationAnalyticsService } from '@/services/translationAnalyticsService';

// Get comprehensive metrics
const metrics = await translationAnalyticsService.getTranslationMetrics(
  { start: new Date('2024-12-01'), end: new Date('2024-12-10') },
  { businessId: 'business_123' }
);

console.log(metrics);
// {
//   totalTranslations: 15420,
//   successfulTranslations: 14891,
//   averageConfidence: 0.87,
//   averageProcessingTime: 1250,
//   totalCost: 234.56,
//   cacheHitRate: 68.3
// }
```

### **Cost Tracking**

Monitor and optimize translation costs:

```typescript
import { costTrackingService } from '@/services/costTrackingService';

// Track translation cost
await costTrackingService.trackTranslationCost({
  businessId: 'business_123',
  provider: 'google',
  sourceLanguage: 'en',
  targetLanguage: 'sw',
  characterCount: 150,
  cost: 0.02
});

// Get cost analysis
const costAnalysis = await costTrackingService.getCostAnalysis(
  'business_123',
  { start: new Date('2024-12-01'), end: new Date('2024-12-10') }
);
```

### **Performance Optimization**

**Caching Strategy:**
- 30-minute cache TTL for translations
- Context-aware cache keys
- 60%+ cache hit rate target

**Batch Processing:**
- Bulk translation for content updates
- Intelligent batching for cost optimization
- Queue management for high-volume operations

**Provider Failover:**
- Primary: Google Cloud Translation API
- Fallback: Azure Translator, AWS Translate
- Automatic provider switching on failures

---

## **🚨 Error Handling**

### **Graceful Degradation**

The system handles failures gracefully:

```typescript
// Translation service with fallback
try {
  const result = await translationService.translateText(text, targetLang);
  return result.translatedText;
} catch (error) {
  console.error('Translation failed:', error);

  // Fallback strategies:
  // 1. Try alternative provider
  // 2. Return cached translation if available
  // 3. Return original text with warning
  // 4. Use pre-translated templates

  return originalText; // Graceful fallback
}
```

### **Error Types & Handling**

```typescript
enum TranslationErrorType {
  API_KEY_INVALID = 'API_KEY_INVALID',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  LANGUAGE_NOT_SUPPORTED = 'LANGUAGE_NOT_SUPPORTED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT = 'TIMEOUT',
  INVALID_INPUT = 'INVALID_INPUT'
}

class TranslationError extends Error {
  constructor(
    public type: TranslationErrorType,
    message: string,
    public provider?: string,
    public retryable: boolean = false
  ) {
    super(message);
  }
}
```

### **Retry Logic**

```typescript
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffMultiplier: 2,
  retryableErrors: [
    TranslationErrorType.NETWORK_ERROR,
    TranslationErrorType.TIMEOUT,
    TranslationErrorType.QUOTA_EXCEEDED
  ]
};
```

---

## **🧪 Testing Framework**

### **Test Suite Overview**

Comprehensive testing with Vitest framework:

```bash
# Run all tests
npm run test

# Run with coverage
npm run test:coverage

# Run specific test suite
npx vitest run src/tests/basic.test.ts

# Watch mode for development
npm run test:watch
```

### **Test Categories**

**1. Basic Translation Tests (16 tests)**
- Translation service functionality
- Language detection accuracy
- Business content translation
- API integration validation
- Performance compliance
- Error handling

**2. Translation Accuracy Tests**
- Multi-language translation quality
- Cultural adaptation validation
- Business context preservation
- Edge case handling

**3. Performance Tests**
- Response time validation (<2s)
- Concurrent request handling
- Cache hit rate optimization
- Memory usage monitoring

**4. Integration Tests**
- End-to-end WhatsApp workflow
- API endpoint validation
- Database integration
- External service integration

### **Mock Services**

Comprehensive mock implementations for testing:

```typescript
import { mockTranslationService } from '@/tests/mockServices';

// Realistic translation simulation
const translation = await mockTranslationService.translateText(
  'Hello world',
  'sw'
);
// Returns: { translatedText: 'Hujambo dunia', confidence: 0.92, cached: false }
```

---

## **🚀 Deployment Guide**

### **Production Environment Setup**

**1. Environment Configuration**
```bash
# Production environment variables
VITE_GOOGLE_TRANSLATE_API_KEY=prod_google_key
VITE_AZURE_TRANSLATOR_KEY=prod_azure_key
VITE_AWS_ACCESS_KEY_ID=prod_aws_key

# Production settings
VITE_TRANSLATION_CACHE_TTL=1800
VITE_TRANSLATION_DAILY_BUDGET=500
VITE_TRANSLATION_MONTHLY_BUDGET=10000
```

**2. Database Migration**
```bash
# Run translation schema migrations
supabase db push

# Verify RLS policies
supabase db diff --schema public
```

**3. API Key Configuration**
- Set up Google Cloud Translation API project
- Configure Azure Translator resource
- Set up AWS Translate service
- Implement key rotation strategy

**4. Monitoring Setup**
- Configure translation cost alerts
- Set up performance monitoring
- Enable error tracking and logging
- Set up usage analytics dashboards

### **Deployment Checklist**

**Pre-deployment:**
- [ ] All environment variables configured
- [ ] Database migrations applied
- [ ] API keys validated and tested
- [ ] Translation providers configured
- [ ] Monitoring and alerting set up
- [ ] Cost budgets and limits configured

**Quality Assurance:**
- [ ] TypeScript compilation: `npx tsc --noEmit`
- [ ] Linting validation: `npm run lint`
- [ ] Production build: `npm run build`
- [ ] Test suite execution: `npm run test`
- [ ] Integration testing completed

**Post-deployment:**
- [ ] Translation services health check
- [ ] API endpoint validation
- [ ] Performance monitoring active
- [ ] Cost tracking operational
- [ ] User language preferences working
- [ ] WhatsApp integration functional

### **Rollback Procedures**

**Emergency Rollback:**
1. Disable translation features via feature flags
2. Revert to previous deployment
3. Restore database to pre-migration state
4. Notify users of temporary English-only mode

**Gradual Rollback:**
1. Reduce translation provider usage
2. Increase cache TTL to reduce API calls
3. Disable specific language pairs if needed
4. Monitor system stability

---

## **📞 Support & Troubleshooting**

### **Common Issues**

**Translation Not Working:**
- Check API key configuration
- Verify provider service status
- Check network connectivity
- Review error logs

**Poor Translation Quality:**
- Adjust confidence thresholds
- Review cultural adaptation settings
- Check business context configuration
- Consider manual translation overrides

**High Costs:**
- Review cache hit rates
- Optimize batch processing
- Adjust translation frequency
- Implement cost alerts

### **Monitoring Dashboards**

Access real-time metrics at:
- Translation Analytics Dashboard
- Cost Tracking Dashboard
- Performance Monitoring Dashboard
- Error Tracking Dashboard

### **Contact Information**

**Technical Support:**
- Email: <EMAIL>
- Documentation: https://docs.afrobot.ai
- GitHub Issues: https://github.com/afrobot/issues

---

**🎉 AfriBot Translation System is now ready for production deployment with comprehensive multilingual support for African SMEs!** 🌍🚀
