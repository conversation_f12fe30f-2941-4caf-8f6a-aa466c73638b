{"numTotalTestSuites": 13, "numPassedTestSuites": 5, "numFailedTestSuites": 8, "numPendingTestSuites": 0, "numTotalTests": 20, "numPassedTests": 15, "numFailedTests": 5, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1757547074566, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["Basic Translation Tests", "Translation Service"], "fullName": "Basic Translation Tests Translation Service should translate text between supported languages", "status": "passed", "title": "should translate text between supported languages", "duration": 161.91640000000007, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Translation Service"], "fullName": "Basic Translation Tests Translation Service should detect language from text content", "status": "passed", "title": "should detect language from text content", "duration": 1.1108000000003813, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Translation Service"], "fullName": "Basic Translation Tests Translation Service should handle translation requests within performance threshold", "status": "passed", "title": "should handle translation requests within performance threshold", "duration": 531.4462999999996, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Business Content Translation"], "fullName": "Basic Translation Tests Business Content Translation should translate business profiles accurately", "status": "passed", "title": "should translate business profiles accurately", "duration": 1071.1657999999989, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Business Content Translation"], "fullName": "Basic Translation Tests Business Content Translation should translate products with consistent quality", "status": "passed", "title": "should translate products with consistent quality", "duration": 5.418300000001182, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Language Detection and Preferences"], "fullName": "Basic Translation Tests Language Detection and Preferences should detect language preferences from phone numbers", "status": "passed", "title": "should detect language preferences from phone numbers", "duration": 2.4732999999996537, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Language Detection and Preferences"], "fullName": "Basic Translation Tests Language Detection and Preferences should handle unknown phone numbers gracefully", "status": "passed", "title": "should handle unknown phone numbers gracefully", "duration": 1.0527000000001863, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Multilingual API Integration"], "fullName": "Basic Translation Tests Multilingual API Integration should provide multilingual business data", "status": "passed", "title": "should provide multilingual business data", "duration": 2.348200000000361, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Multilingual API Integration"], "fullName": "Basic Translation Tests Multilingual API Integration should provide multilingual product data", "status": "passed", "title": "should provide multilingual product data", "duration": 2.2993999999998778, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Multilingual API Integration"], "fullName": "Basic Translation Tests Multilingual API Integration should provide multilingual FAQ data", "status": "passed", "title": "should provide multilingual FAQ data", "duration": 1.776899999998932, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Performance and Quality Metrics"], "fullName": "Basic Translation Tests Performance and Quality Metrics should maintain high confidence scores across languages", "status": "failed", "title": "should maintain high confidence scores across languages", "duration": 984.2794000000013, "failureMessages": ["AssertionError: expected 0.834982117913418 to be greater than or equal to 0.85\n    at C:\\ClaudeProject\\afrobot\\src\\tests\\basic.test.ts:191:23\n    at file:///C:/ClaudeProject/afrobot/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Performance and Quality Metrics"], "fullName": "Basic Translation Tests Performance and Quality Metrics should handle concurrent translation requests efficiently", "status": "passed", "title": "should handle concurrent translation requests efficiently", "duration": 591.1965, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Performance and Quality Metrics"], "fullName": "Basic Translation Tests Performance and Quality Metrics should validate cache hit rate simulation", "status": "passed", "title": "should validate cache hit rate simulation", "duration": 3466.7083000000002, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Error <PERSON> and <PERSON> Cases"], "fullName": "Basic Translation Tests Error Handling and Edge Cases should handle empty strings gracefully", "status": "passed", "title": "should handle empty strings gracefully", "duration": 596.4951999999994, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Error <PERSON> and <PERSON> Cases"], "fullName": "Basic Translation Tests Error <PERSON> and Edge Cases should handle special characters and mixed content", "status": "passed", "title": "should handle special characters and mixed content", "duration": 408.63170000000173, "failureMessages": [], "meta": {}}, {"ancestorTitles": ["Basic Translation Tests", "Error <PERSON> and <PERSON> Cases"], "fullName": "Basic Translation Tests Error Handling and Edge Cases should provide fallback for unsupported scenarios", "status": "passed", "title": "should provide fallback for unsupported scenarios", "duration": 0.8027000000001863, "failureMessages": [], "meta": {}}], "startTime": 1757547175542, "endTime": 1757547183384.8027, "status": "failed", "message": "", "name": "C:/ClaudeProject/afrobot/src/tests/basic.test.ts"}, {"assertionResults": [], "startTime": 1757547074566, "endTime": 1757547074566, "status": "failed", "message": "No test suite found in file C:/ClaudeProject/afrobot/src/tests/endToEndIntegration.test.ts", "name": "C:/ClaudeProject/afrobot/src/tests/endToEndIntegration.test.ts"}, {"assertionResults": [], "startTime": 1757547074566, "endTime": 1757547074566, "status": "failed", "message": "No test suite found in file C:/ClaudeProject/afrobot/src/tests/errorHandlingFallback.test.ts", "name": "C:/ClaudeProject/afrobot/src/tests/errorHandlingFallback.test.ts"}, {"assertionResults": [], "startTime": 1757547074566, "endTime": 1757547074566, "status": "failed", "message": "No test suite found in file C:/ClaudeProject/afrobot/src/tests/performanceValidation.test.ts", "name": "C:/ClaudeProject/afrobot/src/tests/performanceValidation.test.ts"}, {"assertionResults": [{"ancestorTitles": ["Translation Accuracy Tests"], "fullName": "Translation Accuracy Tests should translate text to all supported African languages with high confidence", "status": "failed", "title": "should translate text to all supported African languages with high confidence", "duration": 55.181400000001304, "failureMessages": ["AssertionError: expected +0 to be 7 // Object.is equality\n    at C:\\ClaudeProject\\afrobot\\src\\tests\\translationAccuracy.test.ts:60:36\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///C:/ClaudeProject/afrobot/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "meta": {}}, {"ancestorTitles": ["Translation Accuracy Tests"], "fullName": "Translation Accuracy Tests should translate business profiles accurately across multiple languages", "status": "failed", "title": "should translate business profiles accurately across multiple languages", "duration": 190.58389999999963, "failureMessages": ["AssertionError: expected +0 to be 3 // Object.is equality\n    at C:\\ClaudeProject\\afrobot\\src\\tests\\translationAccuracy.test.ts:100:36\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///C:/ClaudeProject/afrobot/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "meta": {}}, {"ancestorTitles": ["Translation Accuracy Tests"], "fullName": "Translation Accuracy Tests should perform cultural adaptation accurately", "status": "failed", "title": "should perform cultural adaptation accurately", "duration": 11.844600000000355, "failureMessages": ["AssertionError: expected +0 to be 3 // Object.is equality\n    at C:\\ClaudeProject\\afrobot\\src\\tests\\translationAccuracy.test.ts:152:35\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///C:/ClaudeProject/afrobot/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "meta": {}}, {"ancestorTitles": ["Translation Accuracy Tests"], "fullName": "Translation Accuracy Tests should handle edge cases and special characters", "status": "failed", "title": "should handle edge cases and special characters", "duration": 9.48109999999906, "failureMessages": ["AssertionError: expected 0 to be greater than or equal to 6.4\n    at C:\\ClaudeProject\\afrobot\\src\\tests\\translationAccuracy.test.ts:204:26\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///C:/ClaudeProject/afrobot/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "meta": {}}], "startTime": 1757547193376, "endTime": 1757547193644.4812, "status": "failed", "message": "", "name": "C:/ClaudeProject/afrobot/src/tests/translationAccuracy.test.ts"}]}