/**
 * Basic Translation Testing Suite
 * Simple tests to validate the testing framework and core translation functionality
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { TestUtils, TEST_CONFIG, TEST_PHONE_NUMBERS } from './setup';
import { 
  mockTranslationService, 
  mockBusinessContentTranslationService,
  mockMultilingualApiMiddleware,
  mockBusinessApi,
  mockProductsApi,
  mockFaqsApi
} from './mockServices';
import type { SupportedLanguage } from '../types/translation';

describe('Basic Translation Tests', () => {
  beforeAll(async () => {
    console.log('🧪 Setting up Basic Translation Test Suite');
  });

  afterAll(async () => {
    console.log('✅ Basic Translation Test Suite completed');
  });

  describe('Translation Service', () => {
    it('should translate text between supported languages', async () => {
      const sourceText = 'Hello, welcome to our restaurant!';
      const translation = await mockTranslationService.translateText(sourceText, 'en', 'sw');
      
      expect(translation).toBeDefined();
      expect(translation.translatedText).toBeTruthy();
      expect(translation.confidence).toBeGreaterThanOrEqual(0.8);
      expect(translation.sourceLanguage).toBe('en');
      expect(translation.targetLanguage).toBe('sw');
      expect(translation.provider).toBe('mock');
    });

    it('should detect language from text content', async () => {
      const swahiliText = 'Habari za asubuhi';
      const detection = await mockTranslationService.detectLanguage(swahiliText);
      
      expect(detection).toBeDefined();
      expect(detection.language).toBe('sw');
      expect(detection.confidence).toBeGreaterThanOrEqual(0.8);
    });

    it('should handle translation requests within performance threshold', async () => {
      const { result, duration } = await TestUtils.measureExecutionTime(async () => {
        return await mockTranslationService.translateText('Test message', 'en', 'sw');
      });
      
      expect(duration).toBeLessThanOrEqual(TEST_CONFIG.performanceThreshold);
      expect(result.translatedText).toBeTruthy();
    });
  });

  describe('Business Content Translation', () => {
    it('should translate business profiles accurately', async () => {
      const mockBusiness = TestUtils.createMockBusiness();
      const languages: SupportedLanguage[] = ['sw', 'ha', 'fr'];
      
      const translation = await mockBusinessContentTranslationService.translateBusinessProfile(
        mockBusiness,
        languages
      );
      
      expect(translation).toBeDefined();
      expect(translation.originalBusiness).toEqual(mockBusiness);
      expect(Object.keys(translation.translations)).toHaveLength(3);
      
      for (const language of languages) {
        expect(translation.translations[language]).toBeDefined();
        expect(translation.translations[language].name).toBeTruthy();
        expect(translation.translations[language].confidence).toBeGreaterThanOrEqual(0.8);
      }
    });

    it('should translate products with consistent quality', async () => {
      const mockBusiness = TestUtils.createMockBusiness();
      const mockProduct = TestUtils.createMockProduct(mockBusiness.id);
      const languages: SupportedLanguage[] = ['sw', 'fr'];
      
      const translation = await mockBusinessContentTranslationService.translateProduct(
        mockProduct,
        mockBusiness,
        languages
      );
      
      expect(translation).toBeDefined();
      expect(translation.originalProduct).toEqual(mockProduct);
      expect(Object.keys(translation.translations)).toHaveLength(2);
      
      for (const language of languages) {
        expect(translation.translations[language]).toBeDefined();
        expect(translation.translations[language].name).toBeTruthy();
        expect(translation.translations[language].confidence).toBeGreaterThanOrEqual(0.8);
      }
    });
  });

  describe('Language Detection and Preferences', () => {
    it('should detect language preferences from phone numbers', async () => {
      const testCases = [
        { phone: TEST_PHONE_NUMBERS.kenya, expected: 'sw' },
        { phone: TEST_PHONE_NUMBERS.nigeria, expected: 'ha' },
        { phone: TEST_PHONE_NUMBERS.senegal, expected: 'fr' },
        { phone: TEST_PHONE_NUMBERS.ghana, expected: 'en' }
      ];
      
      for (const testCase of testCases) {
        const context = TestUtils.createTestContext(testCase.phone);
        const preference = await mockMultilingualApiMiddleware.detectLanguagePreference(context);
        
        expect(preference).toBeDefined();
        expect(preference.language).toBe(testCase.expected);
        expect(preference.confidence).toBeGreaterThanOrEqual(0.8);
        expect(preference.source).toBe('phone_region');
      }
    });

    it('should handle unknown phone numbers gracefully', async () => {
      const unknownPhone = '+999999999999';
      const context = TestUtils.createTestContext(unknownPhone);
      const preference = await mockMultilingualApiMiddleware.detectLanguagePreference(context);
      
      expect(preference).toBeDefined();
      expect(preference.language).toBe('en'); // Should fallback to English
      expect(preference.confidence).toBeGreaterThanOrEqual(0.8);
    });
  });

  describe('Multilingual API Integration', () => {
    it('should provide multilingual business data', async () => {
      const businessId = 'test_business_123';
      const context = TestUtils.createTestContext(TEST_PHONE_NUMBERS.kenya, 'sw');
      
      const response = await mockBusinessApi.getByIdMultilingual(businessId, context);
      
      expect(response).toBeDefined();
      expect(response.data).toBeDefined();
      expect(response.requestedLanguage).toBe('sw');
      expect(response.translation).toBeDefined();
      expect(response.availableLanguages).toContain('sw');
      expect(response.availableLanguages).toContain('en');
    });

    it('should provide multilingual product data', async () => {
      const businessId = 'test_business_123';
      const context = TestUtils.createTestContext(TEST_PHONE_NUMBERS.senegal, 'fr');
      
      const response = await mockProductsApi.getByBusinessIdMultilingual(businessId, context);
      
      expect(response).toBeDefined();
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.requestedLanguage).toBe('fr');
      expect(response.translation).toBeDefined();
      expect(response.availableLanguages).toContain('fr');
    });

    it('should provide multilingual FAQ data', async () => {
      const businessId = 'test_business_123';
      const context = TestUtils.createTestContext(TEST_PHONE_NUMBERS.ethiopia, 'am');
      
      const response = await mockFaqsApi.getByBusinessIdMultilingual(businessId, context);
      
      expect(response).toBeDefined();
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.requestedLanguage).toBe('am');
      expect(response.translation).toBeDefined();
      expect(response.availableLanguages).toContain('am');
    });
  });

  describe('Performance and Quality Metrics', () => {
    it('should maintain high confidence scores across languages', async () => {
      const testMessage = 'Thank you for your business!';
      const confidenceScores: number[] = [];
      
      for (const language of ['sw', 'ha', 'yo', 'fr'] as SupportedLanguage[]) {
        const translation = await mockTranslationService.translateText(testMessage, 'en', language);
        confidenceScores.push(translation.confidence);
      }
      
      const averageConfidence = confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length;
      expect(averageConfidence).toBeGreaterThanOrEqual(TEST_CONFIG.confidenceThreshold);
      
      // All individual scores should also meet threshold
      for (const score of confidenceScores) {
        expect(score).toBeGreaterThanOrEqual(TEST_CONFIG.confidenceThreshold);
      }
    });

    it('should handle concurrent translation requests efficiently', async () => {
      const concurrentRequests = 10;
      const testMessage = 'Welcome to our service!';
      
      const { result, duration } = await TestUtils.measureExecutionTime(async () => {
        const promises = Array.from({ length: concurrentRequests }, (_, index) => {
          const languages: SupportedLanguage[] = ['sw', 'ha', 'yo', 'fr'];
          const language = languages[index % 4];
          return mockTranslationService.translateText(testMessage, 'en', language);
        });
        
        return await Promise.all(promises);
      });
      
      expect(result).toHaveLength(concurrentRequests);
      expect(duration).toBeLessThanOrEqual(TEST_CONFIG.performanceThreshold * 2); // Allow 2x threshold for concurrent
      
      // All translations should be successful
      for (const translation of result) {
        expect(translation.translatedText).toBeTruthy();
        expect(translation.confidence).toBeGreaterThanOrEqual(0.8);
      }
    });

    it('should validate cache hit rate simulation', async () => {
      const testMessage = 'Cached message test';
      let cacheHits = 0;
      const totalRequests = 10;
      
      for (let i = 0; i < totalRequests; i++) {
        const translation = await mockTranslationService.translateText(testMessage, 'en', 'sw');
        if (translation.cached) {
          cacheHits++;
        }
      }
      
      const cacheHitRate = TestUtils.calculateCacheHitRate(cacheHits, totalRequests);
      
      // Mock service should simulate reasonable cache hit rate
      expect(cacheHitRate).toBeGreaterThanOrEqual(0.5); // At least 50% cache hit rate
      expect(cacheHitRate).toBeLessThanOrEqual(1.0); // Not more than 100%
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle empty strings gracefully', async () => {
      const translation = await mockTranslationService.translateText('', 'en', 'sw');
      
      expect(translation).toBeDefined();
      expect(translation.translatedText).toBeDefined();
      expect(translation.confidence).toBeGreaterThanOrEqual(0);
    });

    it('should handle special characters and mixed content', async () => {
      const specialText = 'Hello! @#$%^&*() 123 <EMAIL>';
      const translation = await mockTranslationService.translateText(specialText, 'en', 'sw');
      
      expect(translation).toBeDefined();
      expect(translation.translatedText).toBeTruthy();
      expect(translation.confidence).toBeGreaterThanOrEqual(0.8);
    });

    it('should provide fallback for unsupported scenarios', async () => {
      const context = TestUtils.createTestContext('invalid-phone', 'invalid' as any);
      const preference = await mockMultilingualApiMiddleware.detectLanguagePreference(context);
      
      expect(preference).toBeDefined();
      expect(preference.language).toBe('en'); // Should fallback to English
      expect(preference.confidence).toBeGreaterThanOrEqual(0);
    });
  });
});
