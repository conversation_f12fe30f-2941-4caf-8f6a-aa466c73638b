/**
 * Test Setup and Configuration
 * Comprehensive testing framework for AfriBot multilingual features
 */

// Test setup utilities

// Test configuration and utilities
export interface TestConfig {
  translationTimeout: number;
  maxRetries: number;
  confidenceThreshold: number;
  performanceThreshold: number;
  cacheHitRateThreshold: number;
}

export const TEST_CONFIG: TestConfig = {
  translationTimeout: 2000, // 2 seconds
  maxRetries: 3,
  confidenceThreshold: 0.85, // 85% minimum confidence
  performanceThreshold: 2000, // 2 seconds max response time
  cacheHitRateThreshold: 0.6 // 60% minimum cache hit rate
};

// Test data for African languages
export const TEST_LANGUAGES = [
  'en', 'sw', 'ha', 'yo', 'am', 'fr', 'ar', 'pt'
] as const;

export const TEST_PHONE_NUMBERS = {
  kenya: '+254712345678',      // Swahili
  tanzania: '+255712345678',   // Swahili
  nigeria: '+234812345678',    // Hausa/Yoruba
  ethiopia: '+251912345678',   // Amharic
  morocco: '+212612345678',    // Arabic
  senegal: '+221771234567',    // French
  angola: '+244912345678',     // Portuguese
  ghana: '+233241234567'       // English
};

export const TEST_BUSINESS_CONTENT = {
  business: {
    name: 'Mama Njeri\'s Restaurant',
    description: 'Authentic Kenyan cuisine with traditional recipes passed down through generations. We serve fresh, locally-sourced ingredients.',
    greeting: 'Welcome to Mama Njeri\'s! How can we help you today?',
    location: 'Nairobi, Kenya'
  },
  products: [
    {
      name: 'Nyama Choma',
      description: 'Grilled meat served with ugali and sukuma wiki',
      price: 800,
      category: 'Main Course'
    },
    {
      name: 'Pilau Rice',
      description: 'Spiced rice cooked with meat and aromatic spices',
      price: 600,
      category: 'Main Course'
    }
  ],
  faqs: [
    {
      question: 'What are your opening hours?',
      answer: 'We are open Monday to Sunday from 8:00 AM to 10:00 PM.'
    },
    {
      question: 'Do you offer delivery services?',
      answer: 'Yes, we deliver within Nairobi for orders above KSh 500.'
    }
  ]
};

// Expected translations for validation
export const EXPECTED_TRANSLATIONS = {
  sw: {
    business: {
      name: 'Mkahawa wa Mama Njeri',
      description: 'Chakula cha asili cha Kikuyu na mapishi ya jadi',
      greeting: 'Karibu Mama Njeri\'s! Tunawezaje kukusaidia leo?'
    },
    products: [
      {
        name: 'Nyama Choma',
        description: 'Nyama ya mchuzi iliyokaangwa na ugali na sukuma wiki'
      }
    ],
    faqs: [
      {
        question: 'Ni masaa gani mnafungua?',
        answer: 'Tunafungua Jumatatu hadi Jumapili kuanzia saa 2:00 asubuhi hadi 4:00 usiku.'
      }
    ]
  },
  ha: {
    business: {
      name: 'Gidan Abinci na Mama Njeri',
      description: 'Abincin gargajiya na girke-girke na kakanni',
      greeting: 'Barka da zuwa Mama Njeri\'s! Yaya za mu taimake ku yau?'
    }
  },
  fr: {
    business: {
      name: 'Restaurant de Mama Njeri',
      description: 'Cuisine kényane authentique avec des recettes traditionnelles',
      greeting: 'Bienvenue chez Mama Njeri\'s! Comment pouvons-nous vous aider aujourd\'hui?'
    }
  }
};

// Test utilities
export class TestUtils {
  /**
   * Measure execution time of a function
   */
  static async measureExecutionTime<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
    const startTime = Date.now();
    const result = await fn();
    const duration = Date.now() - startTime;
    return { result, duration };
  }

  /**
   * Generate random test data
   */
  static generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Create mock business data
   */
  static createMockBusiness(id?: string) {
    return {
      id: id || `business_${Date.now()}`,
      user_id: 'test_user',
      name: TEST_BUSINESS_CONTENT.business.name,
      description: TEST_BUSINESS_CONTENT.business.description,
      contact: '+254712345678',
      location: TEST_BUSINESS_CONTENT.business.location,
      greeting: TEST_BUSINESS_CONTENT.business.greeting,
      tone: 'professional' as const,
      template: 'restaurant',
      sector: 'food',
      language: 'english' as const,
      weekly_reports: true,
      feedback_enabled: true,
      payment_settings: { enabled: true, method: 'paystack' as const, test_mode: true },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  /**
   * Create mock product data
   */
  static createMockProduct(businessId: string, index: number = 0) {
    const product = TEST_BUSINESS_CONTENT.products[index] || TEST_BUSINESS_CONTENT.products[0];
    return {
      id: `product_${Date.now()}_${index}`,
      business_id: businessId,
      name: product.name,
      description: product.description,
      price: product.price,
      category: product.category,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  /**
   * Create mock FAQ data
   */
  static createMockFAQ(businessId: string, index: number = 0) {
    const faq = TEST_BUSINESS_CONTENT.faqs[index] || TEST_BUSINESS_CONTENT.faqs[0];
    return {
      id: `faq_${Date.now()}_${index}`,
      business_id: businessId,
      question: faq.question,
      answer: faq.answer,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  /**
   * Validate translation confidence
   */
  static validateConfidence(confidence: number): boolean {
    return confidence >= TEST_CONFIG.confidenceThreshold;
  }

  /**
   * Validate response time
   */
  static validateResponseTime(duration: number): boolean {
    return duration <= TEST_CONFIG.performanceThreshold;
  }

  /**
   * Calculate cache hit rate
   */
  static calculateCacheHitRate(hits: number, total: number): number {
    return total > 0 ? hits / total : 0;
  }

  /**
   * Validate cache hit rate
   */
  static validateCacheHitRate(hitRate: number): boolean {
    return hitRate >= TEST_CONFIG.cacheHitRateThreshold;
  }

  /**
   * Create test context for API calls
   */
  static createTestContext(phoneNumber?: string, language?: string) {
    return {
      businessId: 'test_business_123',
      userId: 'test_user_456',
      phoneNumber: phoneNumber || TEST_PHONE_NUMBERS.kenya,
      requestedLanguage: language as any,
      userAgent: 'AfriBot-Test/1.0',
      acceptLanguage: language ? `${language},en;q=0.9` : 'en-US,en;q=0.9'
    };
  }

  /**
   * Wait for a specified duration
   */
  static async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Retry a function with exponential backoff
   */
  static async retry<T>(
    fn: () => Promise<T>,
    maxRetries: number = TEST_CONFIG.maxRetries,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          throw lastError;
        }
        
        // Exponential backoff
        const delay = baseDelay * Math.pow(2, attempt);
        await this.wait(delay);
      }
    }
    
    throw lastError!;
  }
}

// Test result interfaces
export interface TestResult {
  name: string;
  passed: boolean;
  duration: number;
  error?: string;
  metadata?: Record<string, any>;
}

export interface TestSuite {
  name: string;
  tests: TestResult[];
  passed: number;
  failed: number;
  totalDuration: number;
  coverage?: number;
}

// Test runner class
export class TestRunner {
  private results: TestSuite[] = [];

  /**
   * Run a test suite
   */
  async runSuite(name: string, tests: Array<() => Promise<TestResult>>): Promise<TestSuite> {
    console.log(`🧪 Running test suite: ${name}`);
    
    const suite: TestSuite = {
      name,
      tests: [],
      passed: 0,
      failed: 0,
      totalDuration: 0
    };

    const startTime = Date.now();

    for (const test of tests) {
      try {
        const result = await test();
        suite.tests.push(result);
        
        if (result.passed) {
          suite.passed++;
          console.log(`  ✅ ${result.name} (${result.duration}ms)`);
        } else {
          suite.failed++;
          console.log(`  ❌ ${result.name} (${result.duration}ms): ${result.error}`);
        }
      } catch (error) {
        const result: TestResult = {
          name: 'Unknown Test',
          passed: false,
          duration: 0,
          error: error instanceof Error ? error.message : String(error)
        };
        suite.tests.push(result);
        suite.failed++;
        console.log(`  ❌ ${result.name}: ${result.error}`);
      }
    }

    suite.totalDuration = Date.now() - startTime;
    this.results.push(suite);

    console.log(`📊 Suite ${name}: ${suite.passed} passed, ${suite.failed} failed (${suite.totalDuration}ms)`);
    return suite;
  }

  /**
   * Get all test results
   */
  getResults(): TestSuite[] {
    return this.results;
  }

  /**
   * Generate test report
   */
  generateReport(): string {
    const totalTests = this.results.reduce((sum, suite) => sum + suite.tests.length, 0);
    const totalPassed = this.results.reduce((sum, suite) => sum + suite.passed, 0);
    const totalFailed = this.results.reduce((sum, suite) => sum + suite.failed, 0);
    const totalDuration = this.results.reduce((sum, suite) => sum + suite.totalDuration, 0);

    let report = `\n🧪 AfriBot Translation Testing Report\n`;
    report += `=====================================\n`;
    report += `Total Tests: ${totalTests}\n`;
    report += `Passed: ${totalPassed}\n`;
    report += `Failed: ${totalFailed}\n`;
    report += `Success Rate: ${((totalPassed / totalTests) * 100).toFixed(1)}%\n`;
    report += `Total Duration: ${totalDuration}ms\n\n`;

    for (const suite of this.results) {
      report += `📋 ${suite.name}\n`;
      report += `  Tests: ${suite.tests.length}\n`;
      report += `  Passed: ${suite.passed}\n`;
      report += `  Failed: ${suite.failed}\n`;
      report += `  Duration: ${suite.totalDuration}ms\n\n`;
    }

    return report;
  }
}

// Export test runner instance
export const testRunner = new TestRunner();
